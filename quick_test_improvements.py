#!/usr/bin/env python3
"""
快速测试剧本改进效果
生成一个简短的测试剧本来验证新的提示词和参数是否有效
"""

import os
import sys
import json
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import logger, ANIMATION_DRAMA_DIR
from modules.langchain_interface import call_llm
from modules.gpt_parameters import (
    LLM_PROVIDER_AZURE,
    MODEL_KEY_GPT41,
    GENERATE_FULL_SCRIPT_LLM_TYPE,
    GENERATE_FULL_SCRIPT_MODEL_KEY
)

def create_test_episode_structure():
    """创建一个简单的测试剧集结构"""
    return {
        "episode_structure": {
            "episode_number": 1,
            "main_plots": ["罗兰面临处决女巫的道德选择", "与传统势力的初次冲突"],
            "main_conflict": "现代理性与中世纪传统的激烈碰撞",
            "emotional_theme": "身份认同与道德坚持",
            "characters": ["罗兰", "巴罗夫", "卡特"],
            "acts": {
                "opening": {
                    "purpose": "立即建立冲突和紧张感",
                    "scenes": 2,
                    "setup_elements": ["处刑台前的道德选择", "权力博弈的开始"]
                },
                "development": {
                    "purpose": "升级冲突和角色对立",
                    "scenes": 2,
                    "escalation_points": ["公开质疑传统", "权威的反击"]
                },
                "ending": {
                    "purpose": "暂时解决但留下更大悬念",
                    "scenes": 1,
                    "resolutions": ["暂缓处决", "更深层冲突的预示"]
                }
            }
        }
    }

def generate_test_script():
    """生成测试剧本"""
    
    logger.info("开始生成测试剧本...")
    
    # 创建测试数据
    test_data = {
        "episode_structure": create_test_episode_structure(),
        "global_outline": {
            "main_theme": "现代人在中世纪世界的生存与改革",
            "tone": "严肃而富有戏剧张力"
        },
        "previous_episode_script": "",
        "chapter_summaries": [
            {
                "summary": "罗兰穿越到中世纪，面临首个重大道德选择",
                "key_events": ["穿越觉醒", "处刑台对峙", "权力博弈"]
            }
        ],
        "style": "现代快节奏音频剧",
        "language": "Chinese",
        "world_tone": "中世纪奇幻",
        "main_conflict": "现代理性与中世纪传统的激烈碰撞"
    }
    
    try:
        # 使用新的提示词生成剧本
        response = call_llm(
            api_function="generate_full_script",
            prompt_data=test_data,
            llm_type=GENERATE_FULL_SCRIPT_LLM_TYPE,
            model_key=GENERATE_FULL_SCRIPT_MODEL_KEY,
            using_cache=False
        )
        
        if response:
            # 保存测试结果
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = Path(ANIMATION_DRAMA_DIR) / "temp" / f"test_improved_script_{timestamp}.txt"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(response)
            
            logger.info(f"测试剧本已生成: {output_file}")
            
            # 简单分析结果
            analyze_test_result(response, output_file)
            
            return response
        else:
            logger.error("生成测试剧本失败：无响应")
            return None
            
    except Exception as e:
        logger.error(f"生成测试剧本时出错: {e}")
        return None

def analyze_test_result(script_content: str, output_file: Path):
    """简单分析测试结果"""
    
    print("\n" + "="*60)
    print("测试剧本快速分析")
    print("="*60)
    
    lines = script_content.split('\n')
    total_lines = len(lines)
    
    # 检查开场是否快速进入冲突
    opening_lines = lines[:10]
    has_immediate_conflict = any(
        keyword in line for line in opening_lines 
        for keyword in ['冲突', '紧张', '对抗', '危机', '选择', '威胁']
    )
    
    # 检查对话密度
    dialogue_lines = [line for line in lines if '：' in line and not line.startswith('—')]
    dialogue_ratio = len(dialogue_lines) / total_lines if total_lines > 0 else 0
    
    # 检查场景转换
    scene_markers = [line for line in lines if '【场景' in line or '【第' in line]
    scene_count = len(scene_markers)
    
    # 检查内心独白
    monologue_lines = [line for line in lines if '——' in line and '独白' in line]
    
    print(f"📊 基本统计:")
    print(f"  总行数: {total_lines}")
    print(f"  场景数: {scene_count}")
    print(f"  对话行数: {len(dialogue_lines)}")
    print(f"  对话密度: {dialogue_ratio:.2%}")
    print(f"  内心独白段落: {len(monologue_lines)}")
    
    print(f"\n🎯 快节奏检查:")
    print(f"  开场立即冲突: {'✅' if has_immediate_conflict else '❌'}")
    print(f"  对话密度充足: {'✅' if dialogue_ratio > 0.3 else '❌'}")
    print(f"  场景数量合理: {'✅' if scene_count >= 3 else '❌'}")
    
    # 检查关键词密度
    engagement_keywords = ['冲突', '紧张', '对抗', '选择', '危机', '秘密', '威胁', '悬念']
    keyword_count = sum(script_content.count(keyword) for keyword in engagement_keywords)
    
    print(f"  吸引力关键词: {keyword_count} 个")
    print(f"  关键词密度: {'✅' if keyword_count > 5 else '❌'}")
    
    # 提供改进建议
    print(f"\n💡 改进建议:")
    if not has_immediate_conflict:
        print("  - 开场需要更快进入冲突")
    if dialogue_ratio < 0.3:
        print("  - 增加对话比例，减少描述")
    if scene_count < 3:
        print("  - 增加场景数量，提高节奏变化")
    if keyword_count <= 5:
        print("  - 增加更多吸引力元素和冲突")
    
    print(f"\n📁 完整剧本保存在: {output_file}")

def compare_with_original():
    """与原始剧本进行对比"""
    
    # 查找最新的原始剧本
    temp_dir = Path(ANIMATION_DRAMA_DIR) / "temp"
    original_files = list(temp_dir.glob("full_script_01_refined_*.txt"))
    
    if original_files:
        latest_original = sorted(original_files)[-1]
        
        print(f"\n🔍 可以与原始剧本对比:")
        print(f"  原始文件: {latest_original}")
        print(f"  运行以下命令进行详细对比:")
        print(f"  python test_script_improvements.py --episode 1")
    else:
        print(f"\n⚠️  未找到原始剧本文件进行对比")

def main():
    """主函数"""
    
    print("🚀 开始快速测试剧本改进效果...")
    
    # 生成测试剧本
    test_script = generate_test_script()
    
    if test_script:
        print("\n✅ 测试剧本生成成功!")
        
        # 提供对比建议
        compare_with_original()
        
        print(f"\n📋 下一步建议:")
        print(f"  1. 查看生成的测试剧本，评估改进效果")
        print(f"  2. 运行完整的质量对比分析")
        print(f"  3. 如果效果良好，重新生成完整剧集")
        
    else:
        print("\n❌ 测试剧本生成失败，请检查配置和网络连接")

if __name__ == "__main__":
    main()
