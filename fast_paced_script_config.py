#!/usr/bin/env python3
"""
快节奏剧本生成的专用配置
针对现代观众期待的快节奏、高吸引力内容优化
"""

# 快节奏剧本生成的核心原则
FAST_PACED_PRINCIPLES = {
    "opening_rules": {
        "max_setup_time": "30秒内必须出现冲突或悬念",
        "no_slow_exposition": "禁止缓慢的背景介绍",
        "immediate_stakes": "立即建立利害关系",
        "hook_types": [
            "mid_action_start",      # 动作中开始
            "conflict_in_progress",  # 冲突进行中
            "mystery_revelation",    # 神秘揭示
            "emotional_crisis",      # 情感危机
            "unexpected_event"       # 意外事件
        ]
    },
    
    "dialogue_efficiency": {
        "multi_purpose_lines": "每句对话必须同时推进情节和揭示角色",
        "subtext_requirement": "必须有潜台词和隐含意义",
        "conflict_in_conversation": "对话中必须有微冲突",
        "no_small_talk": "禁止闲聊和无意义对话",
        "emotional_stakes": "每次对话都要有情感风险"
    },
    
    "scene_structure": {
        "scene_length": "每个场景3-5分钟",
        "conflict_per_scene": "每个场景至少一个微冲突",
        "cliffhanger_endings": "场景结尾必须有悬念或转折",
        "escalation_rule": "每个场景比前一个更紧张",
        "no_filler_scenes": "禁止纯过渡或填充场景"
    },
    
    "character_dynamics": {
        "distinct_voices": "每个角色必须有独特的说话方式",
        "hidden_agendas": "角色必须有隐藏的动机",
        "relationship_tension": "角色间必须有潜在冲突",
        "growth_moments": "每集至少一个角色成长时刻",
        "vulnerability_reveals": "适时展现角色脆弱面"
    }
}

# 现代观众期待的元素
MODERN_AUDIENCE_EXPECTATIONS = {
    "attention_span": {
        "hook_frequency": "每2-3分钟一个小高潮",
        "information_density": "高信息密度，避免冗余",
        "visual_storytelling": "通过对话创造视觉画面",
        "emotional_variety": "情感变化丰富"
    },
    
    "engagement_techniques": {
        "mystery_elements": "保持悬念和未解之谜",
        "character_secrets": "角色秘密逐步揭示",
        "plot_twists": "适度的情节转折",
        "emotional_investment": "让观众关心角色命运",
        "relatable_conflicts": "现代观众能理解的冲突"
    },
    
    "pacing_strategies": {
        "rhythm_variation": "快慢节奏交替",
        "tension_building": "逐步升级的紧张感",
        "release_moments": "适时的情感释放",
        "momentum_maintenance": "保持故事推进动力",
        "surprise_timing": "意外时机的惊喜"
    }
}

# 具体的写作技巧
WRITING_TECHNIQUES = {
    "dialogue_crafting": {
        "interruption_use": "使用打断增加真实感",
        "silence_power": "利用沉默的力量",
        "repetition_for_emphasis": "重复关键词强调",
        "question_hooks": "用问题制造悬念",
        "emotional_subtext": "情感潜台词的运用"
    },
    
    "conflict_creation": {
        "internal_vs_external": "内外冲突的平衡",
        "micro_conflicts": "对话中的小冲突",
        "escalation_patterns": "冲突升级模式",
        "resolution_timing": "解决冲突的时机",
        "new_conflict_seeding": "为新冲突埋下伏笔"
    },
    
    "character_development": {
        "show_dont_tell": "通过行动展现性格",
        "contradiction_use": "利用角色矛盾",
        "growth_through_conflict": "通过冲突促进成长",
        "relationship_dynamics": "关系变化的展现",
        "backstory_integration": "背景故事的巧妙融入"
    }
}

# 质量检查标准
QUALITY_STANDARDS = {
    "engagement_metrics": {
        "hook_frequency": "每页至少一个吸引点",
        "dialogue_efficiency": "90%的对话推进情节",
        "character_distinctiveness": "角色声音识别度>80%",
        "conflict_density": "每场景至少一个冲突点",
        "emotional_range": "涵盖3种以上情感状态"
    },
    
    "pacing_requirements": {
        "opening_impact": "开场30秒内建立冲突",
        "scene_transitions": "场景间无缝衔接",
        "momentum_maintenance": "故事推进不停滞",
        "climax_building": "逐步升级到高潮",
        "resolution_satisfaction": "令人满意的解决"
    },
    
    "technical_standards": {
        "dialogue_authenticity": "对话自然度>85%",
        "character_consistency": "角色行为一致性",
        "plot_logic": "情节逻辑合理性",
        "emotional_authenticity": "情感表达真实性",
        "audio_optimization": "适合音频媒体"
    }
}

# 常见问题及解决方案
COMMON_ISSUES_SOLUTIONS = {
    "slow_pacing": {
        "problem": "节奏过慢，观众失去兴趣",
        "solutions": [
            "删除不必要的描述",
            "增加对话中的冲突",
            "缩短场景长度",
            "增加悬念元素",
            "加快信息揭示速度"
        ]
    },
    
    "flat_dialogue": {
        "problem": "对话平淡，缺乏个性",
        "solutions": [
            "为每个角色设计独特语言模式",
            "增加潜台词和隐含意义",
            "使用更多情感色彩",
            "加入角色特有的表达习惯",
            "创造对话中的小冲突"
        ]
    },
    
    "weak_conflicts": {
        "problem": "冲突不够强烈或明显",
        "solutions": [
            "提高冲突的个人利害关系",
            "增加时间压力",
            "创造道德两难选择",
            "加强角色间的对立",
            "引入外部威胁"
        ]
    },
    
    "predictable_plot": {
        "problem": "情节发展过于可预测",
        "solutions": [
            "增加意外转折",
            "延迟信息揭示",
            "创造假象和误导",
            "角色行为出人意料",
            "改变预期结果"
        ]
    }
}

# 生成参数优化建议
GENERATION_PARAMETERS = {
    "creativity_boost": {
        "temperature": 1.3,  # 提高创意性
        "top_p": 0.92,       # 平衡多样性和连贯性
        "frequency_penalty": 0.6,  # 避免重复表达
        "presence_penalty": 0.5    # 鼓励新颖表达
    },
    
    "prompt_optimization": {
        "specificity": "使用具体而非抽象的指令",
        "examples": "提供优秀对话的示例",
        "constraints": "明确的限制和要求",
        "context": "充分的背景信息",
        "goals": "清晰的目标和期望"
    }
}

def get_fast_paced_config():
    """获取快节奏剧本生成的完整配置"""
    return {
        "principles": FAST_PACED_PRINCIPLES,
        "audience_expectations": MODERN_AUDIENCE_EXPECTATIONS,
        "techniques": WRITING_TECHNIQUES,
        "quality_standards": QUALITY_STANDARDS,
        "common_issues": COMMON_ISSUES_SOLUTIONS,
        "generation_params": GENERATION_PARAMETERS
    }

def validate_script_against_standards(script_content: str) -> dict:
    """根据快节奏标准验证剧本质量"""
    # 这里可以实现具体的验证逻辑
    # 目前返回基本结构
    return {
        "meets_standards": True,
        "issues_found": [],
        "suggestions": [],
        "score": 0.0
    }
