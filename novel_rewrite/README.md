# Novel Rewrite - 小说改编剧本生成系统

基于Key-Event Spine和动态Phase分配的智能小说改编系统，解决剧情漂移和质量控制问题。

## 🚀 快速开始

### 1. 准备输入数据
```json
{
  "chapters": [
    {
      "basic_info": {"chapter_number": 1, "title": "开始"},
      "narrative": {"content": "章节内容摘要..."},
      "key_events": [{"event": "关键事件", "importance": 0.8}],
      "key_characters": ["角色1", "角色2"]
    }
  ]
}
```

### 2. 运行完整流水线
```bash
# 一键运行所有步骤
python full_flow.py input.json --output_dir ./output

# 或分步执行
python build_spine.py input.json --output spine_events.json
python build_phases.py spine_events.json --output phase_allocation.json  
python build_episode.py spine_events.json phase_allocation.json --output_dir ./output
```

### 3. 查看结果
```bash
ls output/
# spine_events.json - Spine事件抽取结果
# phase_allocation.json - Phase分配结果
# episodes.json - 剧集生成结果
# episode_*.txt - 单集剧本文件
# pipeline_summary.md - 总结报告
```

## 📁 项目结构

```
novel_rewrite_flat/
├── spine.py              # Spine事件抽取器
├── alias.py              # 角色别名管理器  
├── phase.py              # 动态Phase分配器
├── drift.py              # 剧情漂移检测器
├── end_state.py          # 跨集状态管理器
├── load_save.py          # 统一I/O操作
├── vector_cache.py       # 向量缓存系统
├── github_bot.py         # GitHub集成机器人
├── chapter_summary.md    # 章节摘要提示词
├── episode_structure.md  # 剧集结构提示词
├── full_script.md        # 完整剧本提示词
├── build_spine.py        # Spine构建流水线
├── build_phases.py       # Phase分配流水线
├── build_episode.py      # 剧集生成流水线
├── full_flow.py          # 完整处理流水线
├── alias_map_seed.json   # 角色别名映射种子
├── model_cfg.yaml        # 模型参数配置
├── smoke_test.py         # 冒烟测试
└── README.md             # 项目说明
```

## 🔧 核心特性

### Key-Event Spine系统
- **轻量级锚点**: 每章1-4个关键事件，平衡忠实度与创作自由
- **双维度评分**: importance(重要性) + tension(张力)分离评估
- **智能筛选**: 重要性优先 + 高张力事件补充

### 动态Phase分配
- **权重计算**: 0.7×tension + 0.3×token_ratio
- **自适应调整**: 偏差>20%自动拆分，<80%自动合并
- **验证规则**: 前3组必须在setup，development不超过60%

### 剧情漂移检测
- **多模型融合**: 向量相似度计算
- **动态阈值**: median + IQR抗极端值干扰
- **三层缓存**: 内存LRU + SQLite + 分片存储

### Yellow队列管理
- **GitHub集成**: 自动创建Issue进行人工审核
- **严重程度分级**: HIGH/MEDIUM/LOW三级分类
- **批量处理**: 支持批量创建和状态同步

## 🧪 测试

```bash
# 运行冒烟测试
python smoke_test.py

# 测试单个模块
python -c "from spine import SpineExtractor; print('Spine module OK')"
python -c "from phase import PhaseAllocator; print('Phase module OK')"
```

## ⚙️ 配置

### 环境变量
```bash
export OPENAI_API_KEY="your-api-key"
export GITHUB_TOKEN="your-github-token"  # 可选
export GITHUB_REPO="owner/repo"          # 可选
```

### 模型配置 (model_cfg.yaml)
```yaml
llm:
  model: "gpt-4"
  temperature: 0.7

quality:
  thresholds:
    base: 0.45
    yellow: 0.5
    red: 0.7

processing:
  spine:
    max_events_per_chapter: 4
  episode:
    target_duration: 12.0
```

## 📊 质量指标

| 指标 | 目标值 | 说明 |
|------|--------|------|
| 剧情漂移率 | <15% | 通过Spine锚点控制 |
| 人工改动率 | <20% | Yellow队列管理 |
| 时长误差 | ±15% | TTS速度估算 |
| 处理速度 | <10min/1k章 | 向量缓存优化 |

## 🔍 监控和调试

### 查看质量报告
```bash
# 检查Yellow队列
find output/ -name "*.json" -exec grep -l "yellow\|red" {} \;

# 查看漂移分数
python -c "
import json
with open('output/episodes.json') as f:
    data = json.load(f)
for ep in data['episodes']:
    qr = ep['quality_report']
    print(f'Episode {ep[\"episode_number\"]}: {qr[\"divergence_score\"]:.3f} ({qr[\"status\"]})')
"
```

### GitHub集成
```bash
# 设置GitHub集成
export GITHUB_TOKEN="your-token"
export GITHUB_REPO="owner/repo"

# 自动创建质量问题Issue
python -c "
from github_bot import GitHubBot
bot = GitHubBot()
# 会自动为Yellow/Red状态的集数创建Issue
"
```

## 🤝 扩展和定制

### 自定义LLM客户端
```python
class CustomLLMClient:
    def call_json_response(self, prompt, expected_fields=None):
        # 实现你的LLM调用逻辑
        return {"spine_events": [...]}

# 使用自定义客户端
from spine import SpineExtractor
extractor = SpineExtractor(CustomLLMClient())
```

### 自定义质量检测
```python
from drift import DriftDetector

class CustomDriftDetector(DriftDetector):
    def calculate_divergence_score(self, script_events, spine_events):
        # 实现自定义的漂移计算逻辑
        return score
```

## 📄 许可证

MIT License

## 📞 支持

- 问题反馈: GitHub Issues
- 功能建议: GitHub Discussions
- 技术支持: 查看代码注释和文档

---

*基于Claude Sonnet 4开发，致力于提供高质量的小说改编解决方案。*
