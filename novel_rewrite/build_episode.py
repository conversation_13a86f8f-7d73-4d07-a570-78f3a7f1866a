#!/usr/bin/env python3
"""
剧集构建流水线
"""

import argparse
import logging
import sys
import os
from typing import Dict, List, Any, Optional

from drift import DriftDetector, QualityReport
from end_state import EndStateManager
from load_save import load_json, save_json, save_text, ensure_dir

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockLLMClient:
    """模拟LLM客户端"""

    def call_json_response(self, prompt, expected_fields=None):
        # 模拟剧集结构生成
        if "episode_structure" in expected_fields:
            return {
                "episode_structure": {
                    "episode_number": 1,
                    "total_scenes": 5,
                    "estimated_duration": 12.0,
                    "spine_coverage": ["1-1", "1-2"],
                    "scenes": [
                        {
                            "scene_number": 1,
                            "title": "开场",
                            "location": "边境镇",
                            "time": "白天",
                            "characters": ["罗兰"],
                            "main_conflict": "建立情境",
                            "emotional_arc": "平静到紧张",
                            "key_events": ["罗兰巡视边境"],
                            "spine_events_covered": ["1-1"],
                            "transition": "淡出",
                            "estimated_duration": 2.5
                        }
                    ]
                },
                "end_state": {
                    "char_states": {
                        "罗兰": {
                            "goal": "保护边境",
                            "risk": "魔鬼威胁",
                            "emotion": "坚定"
                        }
                    },
                    "open_hooks": ["魔鬼何时来袭"],
                    "world_delta": ["边境防务加强"]
                }
            }

        # 模拟剧本生成
        return {
            "script": """# 第1集

## 场景1：边境镇 - 白天

旁白：阳光洒在边境镇的城墙上，罗兰站在城头眺望远方。

罗兰：这里就是我要守护的地方。

旁白：远处传来马蹄声，一队骑兵正在巡逻。

卡特：殿下，边境一切正常。

罗兰：很好，但我们不能掉以轻心。

旁白：罗兰的眼中闪烁着坚定的光芒。"""
        }

def build_episode_allocation(spine_results: Dict[str, Any],
                           phase_allocation: Dict[str, Any],
                           max_episodes: Optional[int] = None) -> List[Dict[str, Any]]:
    """构建剧集分配"""
    episodes = []

    # 获取所有章节，按顺序排列
    chapters = sorted(spine_results.keys(), key=int)

    # 确定每集包含的章节数
    if max_episodes:
        chapters_per_episode = max(1, len(chapters) // max_episodes)
    else:
        chapters_per_episode = 2  # 默认每集2章

    # 分配章节到剧集
    for i in range(0, len(chapters), chapters_per_episode):
        episode_chapters = chapters[i:i + chapters_per_episode]

        # 收集该集的spine事件
        episode_spine_events = []
        for chapter_id in episode_chapters:
            chapter_data = spine_results[chapter_id]
            episode_spine_events.extend(chapter_data["spine_events"])

        episodes.append({
            "episode_number": len(episodes) + 1,
            "chapters": [int(ch) for ch in episode_chapters],
            "spine_events": episode_spine_events
        })

    return episodes

def generate_episode_structure(episode_info: Dict[str, Any], llm_client) -> Dict[str, Any]:
    """生成剧集结构"""
    import sys
    import os
    # 添加主项目路径到sys.path
    main_project_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    if main_project_path not in sys.path:
        sys.path.insert(0, main_project_path)

    from modules.langchain_interface import call_llm_json_response

    # 构建提示词数据
    spine_events = episode_info["spine_events"]
    episode_num = episode_info["episode_number"]
    chapters = episode_info.get("chapters", [])

    prompt_data = {
        "episode_number": episode_num,
        "spine_events": [event['text'] for event in spine_events],
        "chapters": chapters,
        "target_scenes": 5
    }

    response = call_llm_json_response(
        api_function="generate_episode_structure",
        prompt_data=prompt_data,
        expected_fields=["episode_structure"],
        using_cache=True
    )
    return response

def generate_episode_script(episode_structure: Dict[str, Any], llm_client) -> str:
    """生成剧集剧本"""
    import sys
    import os
    # 添加主项目路径到sys.path
    main_project_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    if main_project_path not in sys.path:
        sys.path.insert(0, main_project_path)

    from modules.langchain_interface import call_llm_json_response

    scenes = episode_structure.get("scenes", [])
    episode_num = episode_structure.get("episode_number", 1)
    main_plots = episode_structure.get("main_plots", [])
    characters = episode_structure.get("characters", [])

    prompt_data = {
        "episode_number": episode_num,
        "episode_structure": episode_structure,
        "scenes": scenes,
        "main_plots": main_plots,
        "characters": characters
    }

    response = call_llm_json_response(
        api_function="generate_full_script",
        prompt_data=prompt_data,
        expected_fields=["script"],
        using_cache=True
    )
    return response.get("script", "") if response else ""

def main():
    parser = argparse.ArgumentParser(description="Build Episodes from Phase Allocation")
    parser.add_argument("spine_file", help="Input spine events JSON file")
    parser.add_argument("phase_file", help="Input phase allocation JSON file")
    parser.add_argument("--output_dir", default="./output", help="Output directory")
    parser.add_argument("--max_episodes", type=int, help="Maximum number of episodes")
    parser.add_argument("--compression", type=float, default=0.3, help="Compression ratio")

    args = parser.parse_args()

    try:
        # 确保输出目录存在
        ensure_dir(args.output_dir + "/")

        # 加载输入数据
        logger.info(f"Loading spine events from {args.spine_file}")
        spine_results = load_json(args.spine_file)

        logger.info(f"Loading phase allocation from {args.phase_file}")
        phase_data = load_json(args.phase_file)
        phase_allocation = phase_data.get("allocation", {})

        # 构建剧集分配
        logger.info("Building episode allocation...")
        episode_allocation = build_episode_allocation(
            spine_results, phase_allocation, args.max_episodes
        )

        logger.info(f"Generated allocation for {len(episode_allocation)} episodes")

        # 初始化组件
        drift_detector = DriftDetector()
        end_state_manager = EndStateManager(args.output_dir)

        # 生成剧集
        episodes = []
        quality_reports = []
        previous_end_state = None

        for episode_info in episode_allocation:
            episode_num = episode_info["episode_number"]
            logger.info(f"Generating episode {episode_num}")

            try:
                # 生成剧集结构
                structure_response = generate_episode_structure(episode_info, None)
                episode_structure = structure_response.get("episode_structure", {}) if structure_response else {}

                # 生成剧本
                episode_script = generate_episode_script(episode_structure, None)

                # 质量检测
                spine_events_text = [event["text"] for event in episode_info["spine_events"]]
                quality_report = drift_detector.detect_drift(
                    episode_script, spine_events_text, episode_num
                )

                # 生成结束状态
                end_state = end_state_manager.generate_end_state(
                    episode_script, episode_num, previous_end_state
                )

                # 保存剧集数据
                episode_data = {
                    "episode_number": episode_num,
                    "chapters": episode_info["chapters"],
                    "spine_events": episode_info["spine_events"],
                    "structure": episode_structure,
                    "script": episode_script,
                    "end_state": end_state.to_dict(),
                    "quality_report": quality_report.to_dict()
                }

                episodes.append(episode_data)
                quality_reports.append(quality_report)

                # 保存单独的剧本文件
                script_file = os.path.join(args.output_dir, f"episode_{episode_num:02d}.txt")
                save_text(episode_script, script_file)

                logger.info(f"Episode {episode_num} completed - Status: {quality_report.status}")

                previous_end_state = end_state

            except Exception as e:
                logger.error(f"Failed to generate episode {episode_num}: {e}")
                continue

        # 保存完整结果
        results = {
            "episodes": episodes,
            "total_episodes": len(episodes),
            "compression_ratio": args.compression,
            "quality_summary": {
                "total": len(quality_reports),
                "green": len([r for r in quality_reports if r.status == "green"]),
                "yellow": len([r for r in quality_reports if r.status == "yellow"]),
                "red": len([r for r in quality_reports if r.status == "red"])
            }
        }

        output_file = os.path.join(args.output_dir, "episodes.json")
        save_json(results, output_file)
        logger.info(f"Saved episode results to {output_file}")

        # 输出统计信息
        logger.info(f"Episode generation completed:")
        logger.info(f"  Total episodes: {len(episodes)}")
        logger.info(f"  Quality distribution:")
        logger.info(f"    Green: {results['quality_summary']['green']}")
        logger.info(f"    Yellow: {results['quality_summary']['yellow']}")
        logger.info(f"    Red: {results['quality_summary']['red']}")

        return 0

    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
