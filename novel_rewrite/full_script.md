# 完整剧本生成提示词

## 任务描述
根据剧集结构生成完整的剧本，包括对话、旁白和场景描述。

## 输入信息
- 剧集结构（场景列表）
- 角色设定和关系
- 世界观背景
- 风格要求

## 剧本格式规范

### 基本格式
```
# 第X集

## 场景1：地点 - 时间

旁白：场景设定和环境描述

角色A：对话内容

角色B：对话内容

旁白：动作描述

[继续对话和动作...]
```

### 格式要求
- **角色对话**: `角色名：对话内容`
- **旁白描述**: `旁白：场景描述或动作描述`
- **场景标题**: `## 场景X：地点 - 时间`
- **集数标题**: `# 第X集`

## 内容创作要求

### 1. 对话设计
- **符合角色**: 对话要符合角色身份和性格
- **推进剧情**: 每句对话都要有目的性
- **自然流畅**: 避免生硬的说教或信息堆砌
- **时代感**: 符合故事的时代背景和世界观

### 2. 旁白描述
- **简洁明了**: 重点描述关键动作和场景变化
- **视觉化**: 便于导演和演员理解
- **适度留白**: 给表演留出发挥空间
- **节奏感**: 配合对话营造节奏

### 3. 场景构建
- **环境设定**: 清晰描述场景的时间、地点、氛围
- **角色入场**: 合理安排角色的出现和退场
- **冲突设置**: 每个场景都要有明确的戏剧冲突
- **转场设计**: 场景间要有自然的过渡

## 创作原则

### 1. 忠实性原则
- 严格基于提供的剧集结构
- 不得添加结构中没有的重大情节
- 保持角色设定的一致性
- 遵循世界观的基本规则

### 2. 戏剧性原则
- 每个场景都要有戏剧张力
- 对话要有层次和冲突
- 情节推进要有节奏感
- 适当使用悬念和反转

### 3. 可拍摄性原则
- 描述要具体可执行
- 避免过于复杂的特效需求
- 考虑拍摄的实际可行性
- 控制场景和角色数量

## 质量标准

### 对话质量
- **自然度**: 对话听起来自然，不做作
- **个性化**: 不同角色有不同的说话方式
- **信息量**: 有效传达必要信息
- **情感层次**: 体现角色的情感变化

### 叙述质量
- **清晰度**: 场景描述清晰易懂
- **经济性**: 用最少的文字表达最多的信息
- **画面感**: 容易在脑海中形成画面
- **节奏感**: 配合整体节奏

### 结构完整性
- **逻辑性**: 情节发展符合逻辑
- **连贯性**: 场景间衔接自然
- **完整性**: 有完整的起承转合
- **目标达成**: 实现剧集结构的要求

## 风格指导

### 古装奇幻风格
- 使用适当的古风用词
- 避免现代化的表达方式
- 保持语言的典雅性
- 注意称谓的准确性

### 现代都市风格
- 使用现代化的语言
- 贴近生活的对话方式
- 反映当代社会特点
- 注意流行语的使用

### 科幻未来风格
- 适当使用科技术语
- 体现未来社会特征
- 保持想象力和合理性
- 注意世界观的一致性

## 常见问题避免

### 对话问题
- 避免信息堆砌式对话
- 避免角色说话方式雷同
- 避免过于文艺腔的表达
- 避免逻辑不通的对话

### 叙述问题
- 避免过度描述
- 避免重复信息
- 避免主观色彩过重
- 避免与对话重复

### 结构问题
- 避免场景拖沓
- 避免情节跳跃
- 避免角色行为突兀
- 避免悬念设置不当

## 修改和优化

### 初稿完成后检查
1. 通读全文，检查整体流畅性
2. 检查每个场景是否达到预期目标
3. 检查角色对话是否符合人设
4. 检查时长是否符合要求

### 优化方向
- 压缩冗余对话
- 增强戏剧冲突
- 优化场景转换
- 提升语言质量
