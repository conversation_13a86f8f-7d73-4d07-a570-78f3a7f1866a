"""
GitHub集成机器人 - Yellow队列管理
"""

import os
import logging
from typing import Dict, List, Any, Optional
import requests
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class GitHubBot:
    """GitHub集成机器人"""
    
    def __init__(self, token: Optional[str] = None, repo: Optional[str] = None):
        self.token = token or os.getenv("GITHUB_TOKEN")
        self.repo = repo or os.getenv("GITHUB_REPO")  # format: "owner/repo"
        
        if not self.token:
            logger.warning("GitHub token not provided, GitHub integration disabled")
        if not self.repo:
            logger.warning("GitHub repo not provided, GitHub integration disabled")
        
        self.base_url = "https://api.github.com"
        self.headers = {
            "Authorization": f"token {self.token}",
            "Accept": "application/vnd.github.v3+json",
            "Content-Type": "application/json"
        }
    
    def create_yellow_issue(self, episode_num: int, quality_report: Dict[str, Any]) -> Optional[str]:
        """为Yellow状态的集数创建GitHub Issue"""
        if not self.token or not self.repo:
            logger.warning("GitHub not configured, skipping issue creation")
            return None
        
        try:
            # 构建Issue标题和内容
            title = f"Episode {episode_num:02d} Quality Issues - {quality_report['severity']}"
            body = self._build_issue_body(episode_num, quality_report)
            
            # 确定标签
            labels = self._get_issue_labels(quality_report)
            
            # 创建Issue
            issue_data = {
                "title": title,
                "body": body,
                "labels": labels
            }
            
            response = requests.post(
                f"{self.base_url}/repos/{self.repo}/issues",
                headers=self.headers,
                json=issue_data
            )
            
            if response.status_code == 201:
                issue_url = response.json()["html_url"]
                logger.info(f"Created GitHub issue for episode {episode_num}: {issue_url}")
                return issue_url
            else:
                logger.error(f"Failed to create GitHub issue: {response.status_code} {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to create GitHub issue: {e}")
            return None
    
    def _build_issue_body(self, episode_num: int, quality_report: Dict[str, Any]) -> str:
        """构建Issue内容"""
        body = f"""# Episode {episode_num:02d} Quality Review

## Quality Metrics
- **Divergence Score**: {quality_report['divergence_score']:.3f}
- **Threshold**: {quality_report['threshold']:.3f}
- **Status**: {quality_report['status'].upper()}
- **Severity**: {quality_report['severity']}
- **Timestamp**: {quality_report['timestamp']}

## Issues Detected

### Missing Spine Events
"""
        
        if quality_report.get('missing_spine_events'):
            for event in quality_report['missing_spine_events']:
                body += f"- {event}\n"
        else:
            body += "- No missing spine events detected\n"
        
        body += "\n### Contradicted Events\n"
        
        if quality_report.get('contradicted_events'):
            for event in quality_report['contradicted_events']:
                body += f"- {event}\n"
        else:
            body += "- No contradicted events detected\n"
        
        body += "\n## Improvement Suggestions\n"
        
        if quality_report.get('suggestions'):
            for i, suggestion in enumerate(quality_report['suggestions'], 1):
                body += f"{i}. {suggestion}\n"
        else:
            body += "- No specific suggestions available\n"
        
        body += f"""
## Next Steps
- [ ] Review the episode script
- [ ] Address missing spine events
- [ ] Resolve contradictions
- [ ] Re-run quality check
- [ ] Close this issue when resolved

---
*Auto-generated by novel_rewrite quality monitor*
*Episode: {episode_num} | Score: {quality_report['divergence_score']:.3f} | Threshold: {quality_report['threshold']:.3f}*
"""
        
        return body
    
    def _get_issue_labels(self, quality_report: Dict[str, Any]) -> List[str]:
        """获取Issue标签"""
        labels = ["quality-issue", "auto-generated"]
        
        # 严重程度标签
        severity = quality_report.get('severity', 'MEDIUM').lower()
        labels.append(f"severity-{severity}")
        
        # 状态标签
        status = quality_report.get('status', 'yellow')
        if status == "red":
            labels.append("critical")
        
        return labels
    
    def update_issue_status(self, issue_number: int, status: str, comment: str = None) -> bool:
        """更新Issue状态"""
        if not self.token or not self.repo:
            return False
        
        try:
            # 添加评论
            if comment:
                comment_data = {"body": comment}
                requests.post(
                    f"{self.base_url}/repos/{self.repo}/issues/{issue_number}/comments",
                    headers=self.headers,
                    json=comment_data
                )
            
            # 如果状态是resolved，关闭Issue
            if status.lower() == "resolved":
                issue_data = {"state": "closed"}
                response = requests.patch(
                    f"{self.base_url}/repos/{self.repo}/issues/{issue_number}",
                    headers=self.headers,
                    json=issue_data
                )
                
                if response.status_code == 200:
                    logger.info(f"Closed GitHub issue #{issue_number}")
                    return True
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update issue status: {e}")
            return False
    
    def list_open_quality_issues(self) -> List[Dict[str, Any]]:
        """列出所有开放的质量问题Issue"""
        if not self.token or not self.repo:
            return []
        
        try:
            response = requests.get(
                f"{self.base_url}/repos/{self.repo}/issues",
                headers=self.headers,
                params={
                    "labels": "quality-issue",
                    "state": "open"
                }
            )
            
            if response.status_code == 200:
                issues = response.json()
                return [
                    {
                        "number": issue["number"],
                        "title": issue["title"],
                        "url": issue["html_url"],
                        "created_at": issue["created_at"],
                        "labels": [label["name"] for label in issue["labels"]]
                    }
                    for issue in issues
                ]
            else:
                logger.error(f"Failed to list issues: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Failed to list quality issues: {e}")
            return []
    
    def create_batch_issues(self, quality_reports: List[Dict[str, Any]]) -> Dict[int, Optional[str]]:
        """批量创建Issue"""
        results = {}
        
        for report in quality_reports:
            episode_num = report.get('episode_num')
            if episode_num and report.get('status') in ['yellow', 'red']:
                issue_url = self.create_yellow_issue(episode_num, report)
                results[episode_num] = issue_url
        
        logger.info(f"Created {len([url for url in results.values() if url])} GitHub issues")
        return results
    
    def sync_yellow_queue_status(self, yellow_queue_items: List[Dict[str, Any]]) -> None:
        """同步Yellow队列状态到GitHub"""
        if not self.token or not self.repo:
            return
        
        # 获取现有的质量问题Issue
        open_issues = self.list_open_quality_issues()
        
        # 创建Episode号到Issue号的映射
        episode_to_issue = {}
        for issue in open_issues:
            # 从标题中提取Episode号
            title = issue["title"]
            if "Episode" in title:
                try:
                    episode_num = int(title.split("Episode")[1].split()[0])
                    episode_to_issue[episode_num] = issue["number"]
                except (ValueError, IndexError):
                    continue
        
        # 检查哪些Episode已经解决但Issue还开着
        resolved_episodes = set()
        for item in yellow_queue_items:
            episode_num = item.get("episode_num")
            if episode_num and item.get("status") == "resolved":
                resolved_episodes.add(episode_num)
        
        # 关闭已解决的Issue
        for episode_num in resolved_episodes:
            if episode_num in episode_to_issue:
                issue_number = episode_to_issue[episode_num]
                self.update_issue_status(
                    issue_number, 
                    "resolved", 
                    "Episode quality issues have been resolved. Closing this issue."
                )
    
    def get_repo_stats(self) -> Dict[str, Any]:
        """获取仓库统计信息"""
        if not self.token or not self.repo:
            return {}
        
        try:
            # 获取仓库信息
            response = requests.get(
                f"{self.base_url}/repos/{self.repo}",
                headers=self.headers
            )
            
            if response.status_code == 200:
                repo_data = response.json()
                
                # 获取质量问题统计
                quality_issues = self.list_open_quality_issues()
                
                return {
                    "repo_name": repo_data["full_name"],
                    "open_quality_issues": len(quality_issues),
                    "total_issues": repo_data["open_issues_count"],
                    "last_updated": repo_data["updated_at"]
                }
            else:
                return {}
                
        except Exception as e:
            logger.error(f"Failed to get repo stats: {e}")
            return {}
