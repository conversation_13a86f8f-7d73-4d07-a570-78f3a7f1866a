"""
跨集状态管理器
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import hashlib
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class CharacterState:
    """角色状态"""
    goal: str      # ≤10字
    risk: str      # ≤10字  
    emotion: str   # ≤10字
    
    def to_dict(self) -> Dict[str, str]:
        return {
            "goal": self.goal,
            "risk": self.risk,
            "emotion": self.emotion
        }

@dataclass
class EndState:
    """集结束状态"""
    char_states: Dict[str, CharacterState]
    open_hooks: List[str]      # ≤5个观众已知的悬念
    world_delta: List[str]     # ≤3个设定或势力的永久改变
    checksum: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "char_states": {k: v.to_dict() for k, v in self.char_states.items()},
            "open_hooks": self.open_hooks,
            "world_delta": self.world_delta,
            "checksum": self.checksum
        }

class EndStateManager:
    """跨集状态管理器"""
    
    def __init__(self, output_dir: str = "./output"):
        self.output_dir = output_dir
        self.current_state = None
        
    def generate_end_state(self, episode_script: str, episode_num: int, previous_state: Optional[EndState] = None) -> EndState:
        """从剧集脚本生成结束状态"""
        try:
            # 分析角色状态
            char_states = self._analyze_character_states(episode_script, previous_state)
            
            # 提取未解决的悬念
            open_hooks = self._extract_open_hooks(episode_script, previous_state)
            
            # 识别世界变化
            world_delta = self._identify_world_changes(episode_script, previous_state)
            
            # 计算校验和
            checksum = self._calculate_checksum(char_states, open_hooks, world_delta)
            
            # 创建结束状态
            end_state = EndState(
                char_states=char_states,
                open_hooks=open_hooks,
                world_delta=world_delta,
                checksum=checksum
            )
            
            # 保存状态
            self._save_end_state(end_state, episode_num)
            self.current_state = end_state
            
            logger.info(f"Generated end state for episode {episode_num}")
            return end_state
            
        except Exception as e:
            logger.error(f"Failed to generate end state for episode {episode_num}: {e}")
            return self._create_fallback_state(previous_state)
    
    def _analyze_character_states(self, script: str, previous_state: Optional[EndState] = None) -> Dict[str, CharacterState]:
        """分析角色状态变化"""
        char_states = {}
        
        # 从脚本中提取角色
        characters = self._extract_characters_from_script(script)
        
        for character in characters:
            # 分析角色的目标、风险、情感
            goal = self._analyze_character_goal(character, script)
            risk = self._analyze_character_risk(character, script)
            emotion = self._analyze_character_emotion(character, script)
            
            char_states[character] = CharacterState(
                goal=goal[:10],  # 限制长度
                risk=risk[:10],
                emotion=emotion[:10]
            )
        
        # 如果有前一状态，继承未变化的角色
        if previous_state:
            for char, state in previous_state.char_states.items():
                if char not in char_states:
                    char_states[char] = state
        
        return char_states
    
    def _extract_characters_from_script(self, script: str) -> List[str]:
        """从脚本中提取角色名称"""
        # 简单的角色名提取（基于对话格式）
        character_pattern = r'^([一-龯]{2,4})[:：]'
        characters = set()
        
        for line in script.split('\n'):
            match = re.match(character_pattern, line.strip())
            if match:
                characters.add(match.group(1))
        
        # 也从叙述中提取
        narrative_pattern = r'([一-龯]{2,4})(说|道|想|看|听|感到|决定)'
        narrative_chars = re.findall(narrative_pattern, script)
        for char, _ in narrative_chars:
            characters.add(char)
        
        return list(characters)[:5]  # 最多5个主要角色
    
    def _analyze_character_goal(self, character: str, script: str) -> str:
        """分析角色目标"""
        # 查找与角色相关的目标关键词
        goal_keywords = {
            '寻找': '寻找',
            '保护': '保护',
            '复仇': '复仇',
            '逃脱': '逃脱',
            '证明': '证明',
            '拯救': '拯救',
            '统治': '统治',
            '学习': '学习'
        }
        
        for keyword, goal in goal_keywords.items():
            if character in script and keyword in script:
                # 检查角色和关键词的距离
                char_positions = [m.start() for m in re.finditer(character, script)]
                keyword_positions = [m.start() for m in re.finditer(keyword, script)]
                
                for char_pos in char_positions:
                    for keyword_pos in keyword_positions:
                        if abs(char_pos - keyword_pos) < 100:  # 距离较近
                            return goal
        
        return "未明确"
    
    def _analyze_character_risk(self, character: str, script: str) -> str:
        """分析角色风险"""
        risk_keywords = {
            '危险': '生命危险',
            '威胁': '受到威胁',
            '追杀': '被追杀',
            '背叛': '被背叛',
            '失败': '任务失败',
            '暴露': '身份暴露',
            '陷阱': '陷入陷阱'
        }
        
        for keyword, risk in risk_keywords.items():
            if character in script and keyword in script:
                return risk
        
        return "相对安全"
    
    def _analyze_character_emotion(self, character: str, script: str) -> str:
        """分析角色情感"""
        emotion_keywords = {
            '愤怒': '愤怒',
            '悲伤': '悲伤',
            '恐惧': '恐惧',
            '喜悦': '喜悦',
            '担忧': '担忧',
            '坚定': '坚定',
            '困惑': '困惑',
            '绝望': '绝望'
        }
        
        for keyword, emotion in emotion_keywords.items():
            if character in script and keyword in script:
                return emotion
        
        return "平静"
    
    def _extract_open_hooks(self, script: str, previous_state: Optional[EndState] = None) -> List[str]:
        """提取未解决的悬念"""
        hooks = []
        
        # 悬念关键词
        hook_patterns = [
            r'([一-龯\s]{5,20})(的真相|的秘密|的身份)',
            r'(谁是|什么是|为什么)([一-龯\s]{5,20})',
            r'([一-龯\s]{5,20})(会如何|将会|能否)'
        ]
        
        for pattern in hook_patterns:
            matches = re.findall(pattern, script)
            for match in matches:
                hook = ''.join(match).strip()
                if 5 <= len(hook) <= 20:
                    hooks.append(hook)
        
        # 继承前一状态的未解决悬念
        if previous_state:
            for hook in previous_state.open_hooks:
                # 检查是否在本集中解决
                if not any(keyword in script for keyword in hook.split()):
                    hooks.append(hook)
        
        # 去重并限制数量
        hooks = list(set(hooks))[:5]
        
        return hooks
    
    def _identify_world_changes(self, script: str, previous_state: Optional[EndState] = None) -> List[str]:
        """识别世界变化"""
        changes = []
        
        # 世界变化关键词
        change_keywords = {
            '新法律': '法律变更',
            '新技术': '技术进步',
            '新发现': '重大发现',
            '政变': '政治变化',
            '战争': '战争爆发',
            '和平': '和平协议',
            '联盟': '势力联盟'
        }
        
        for keyword, change in change_keywords.items():
            if keyword in script:
                changes.append(change)
        
        # 限制数量
        return changes[:3]
    
    def _calculate_checksum(self, char_states: Dict[str, CharacterState], 
                           open_hooks: List[str], world_delta: List[str]) -> str:
        """计算状态校验和"""
        data = {
            "char_states": {k: v.to_dict() for k, v in char_states.items()},
            "open_hooks": sorted(open_hooks),
            "world_delta": sorted(world_delta)
        }
        return hashlib.sha256(json.dumps(data, sort_keys=True).encode()).hexdigest()[:16]
    
    def _save_end_state(self, end_state: EndState, episode_num: int) -> None:
        """保存结束状态到文件"""
        import os
        
        os.makedirs(self.output_dir, exist_ok=True)
        filename = f"{self.output_dir}/end_state_ep{episode_num:02d}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(end_state.to_dict(), f, ensure_ascii=False, indent=2)
        
        logger.info(f"Saved end state to {filename}")
    
    def load_end_state(self, episode_num: int) -> Optional[EndState]:
        """加载指定集数的结束状态"""
        filename = f"{self.output_dir}/end_state_ep{episode_num:02d}.json"
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 重建CharacterState对象
            char_states = {}
            for char, state_data in data['char_states'].items():
                char_states[char] = CharacterState(**state_data)
            
            return EndState(
                char_states=char_states,
                open_hooks=data['open_hooks'],
                world_delta=data['world_delta'],
                checksum=data['checksum']
            )
        except FileNotFoundError:
            logger.warning(f"End state file not found: {filename}")
            return None
        except Exception as e:
            logger.error(f"Failed to load end state from {filename}: {e}")
            return None
    
    def _create_fallback_state(self, previous_state: Optional[EndState] = None) -> EndState:
        """创建兜底状态"""
        if previous_state:
            return previous_state
        
        return EndState(
            char_states={},
            open_hooks=[],
            world_delta=[],
            checksum="fallback"
        )
