"""
Spine事件抽取器 - 核心算法
"""

import re
import json
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SpineEvent:
    """Spine事件数据结构"""
    id: str
    type: str  # "plot" | "emotion"
    text: str
    importance: float  # 0-1
    tension: float     # 0-1
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "type": self.type,
            "text": self.text,
            "importance": self.importance,
            "tension": self.tension
        }

class SpineExtractor:
    """Spine事件抽取器"""
    
    def __init__(self, llm_client, alias_map: Dict[str, List[str]] = None):
        self.llm_client = llm_client
        self.alias_map = alias_map or {}
        
    def extract_spine_events(self, chapter_summary: Dict[str, Any], max_events: int = 4) -> List[SpineEvent]:
        """从章节摘要中抽取Spine事件"""
        try:
            # 标准化角色名称
            normalized_summary = self._normalize_chapter_summary(chapter_summary)
            
            # 计算基础分数
            tension_score = self.calculate_tension_score(normalized_summary)
            
            # 使用LLM抽取事件
            spine_events = self._extract_with_llm(normalized_summary, max_events)
            
            # 规则验证和筛选
            filtered_events = self._filter_and_rank_events(spine_events, tension_score, max_events)
            
            chapter_num = chapter_summary.get('basic_info', {}).get('chapter_number', 'unknown')
            logger.info(f"Chapter {chapter_num}: extracted {len(filtered_events)} spine events")
            
            return filtered_events
            
        except Exception as e:
            logger.error(f"Failed to extract spine events: {e}")
            return self._fallback_extraction(chapter_summary)
    
    def calculate_tension_score(self, chapter_summary: Dict[str, Any]) -> float:
        """计算章节张力分数"""
        content = chapter_summary.get('narrative', {}).get('content', '')
        events = chapter_summary.get('key_events', [])
        
        score = 0.0
        
        # 冲突关键词 (权重 *2)
        conflict_keywords = ['冲突', '战斗', '争执', '对抗', '危险', '威胁', '死亡', '背叛', '决战', '生死']
        for keyword in conflict_keywords:
            score += content.count(keyword) * 0.2
        
        # 标点符号强度
        score += content.count('！') * 0.1
        score += content.count('？') * 0.05
        score += content.count('……') * 0.03
        
        # 事件数量和类型
        score += len(events) * 0.1
        
        # 角色数量 (/10)
        characters = chapter_summary.get('key_characters', [])
        score += len(characters) * 0.05
        
        # 标准化到0-1
        return min(score, 1.0)
    
    def _normalize_chapter_summary(self, chapter_summary: Dict[str, Any]) -> Dict[str, Any]:
        """标准化章节摘要中的角色名称"""
        normalized = chapter_summary.copy()
        
        # 标准化内容文本
        if 'narrative' in normalized and 'content' in normalized['narrative']:
            normalized['narrative']['content'] = self._standardize_names(
                normalized['narrative']['content']
            )
        
        return normalized
    
    def _standardize_names(self, text: str) -> str:
        """标准化角色名称"""
        if not self.alias_map:
            return text
        
        # 构建反向映射
        reverse_map = {}
        for canonical, aliases in self.alias_map.items():
            reverse_map[canonical] = canonical
            for alias in aliases:
                reverse_map[alias] = canonical
        
        # 按长度排序，先替换长的别名
        sorted_aliases = sorted(reverse_map.keys(), key=len, reverse=True)
        
        result = text
        for alias in sorted_aliases:
            canonical = reverse_map[alias]
            if alias != canonical:
                pattern = r'\b' + re.escape(alias) + r'\b'
                result = re.sub(pattern, canonical, result)
        
        return result
    
    def _extract_with_llm(self, chapter_summary: Dict[str, Any], max_events: int) -> List[SpineEvent]:
        """使用LLM抽取事件"""
        prompt = self._build_extraction_prompt(chapter_summary, max_events)
        
        response = self.llm_client.call_json_response(
            prompt=prompt,
            expected_fields=["spine_events"]
        )
        
        spine_events = []
        for event_data in response.get('spine_events', []):
            try:
                spine_event = SpineEvent(
                    id=event_data['id'],
                    type=event_data['type'],
                    text=event_data['text'],
                    importance=event_data['importance'],
                    tension=event_data['tension']
                )
                spine_events.append(spine_event)
            except (KeyError, ValueError) as e:
                logger.warning(f"Invalid spine event data: {event_data}, error: {e}")
                continue
        
        return spine_events
    
    def _build_extraction_prompt(self, chapter_summary: Dict[str, Any], max_events: int) -> str:
        """构建抽取提示词"""
        chapter_num = chapter_summary.get('basic_info', {}).get('chapter_number', 'unknown')
        content = chapter_summary.get('narrative', {}).get('content', '')
        
        prompt = f"""# Spine事件抽取任务

从第{chapter_num}章摘要中抽取关键的Spine事件。

## 章节内容
{content}

## 抽取要求
1. 最多抽取{max_events}个事件
2. 按importance优先选择前2个，若tension>0.7再添加1-2个
3. 每个事件描述≤25 tokens

## 输出格式
```json
{{
  "spine_events": [
    {{
      "id": "{chapter_num}-1",
      "type": "plot",
      "text": "简洁的事件描述",
      "importance": 0.85,
      "tension": 0.6
    }}
  ]
}}
```

请开始抽取："""
        
        return prompt
    
    def _filter_and_rank_events(self, events: List[SpineEvent], chapter_tension: float, max_events: int) -> List[SpineEvent]:
        """筛选和排序事件"""
        if not events:
            return []
        
        # 按重要性排序，取前2个
        events_by_importance = sorted(events, key=lambda x: x.importance, reverse=True)
        selected_events = events_by_importance[:2]
        
        # 如果章节张力高，再添加高张力事件
        if chapter_tension > 0.7:
            high_tension_events = [e for e in events if e.tension > 0.7 and e not in selected_events]
            high_tension_events.sort(key=lambda x: x.tension, reverse=True)
            selected_events.extend(high_tension_events[:2])
        
        # 限制总数
        return selected_events[:max_events]
    
    def _fallback_extraction(self, chapter_summary: Dict[str, Any]) -> List[SpineEvent]:
        """兜底抽取方法"""
        events = []
        chapter_num = chapter_summary.get('basic_info', {}).get('chapter_number', 0)
        
        # 从key_events中提取
        key_events = chapter_summary.get('key_events', [])
        for i, event in enumerate(key_events[:2]):
            spine_event = SpineEvent(
                id=f"{chapter_num}-{i+1}",
                type="plot",
                text=event.get('event', '未知事件')[:50],
                importance=0.7,
                tension=0.5
            )
            events.append(spine_event)
        
        return events
