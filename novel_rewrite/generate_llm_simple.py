#!/usr/bin/env python3
"""
简化版本：直接使用原有的generate_episodes.py方式生成剧集
"""

import json
import sys
import os
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_paths():
    """设置路径"""
    # 添加主项目路径到sys.path
    main_project_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    if main_project_path not in sys.path:
        sys.path.insert(0, main_project_path)

def generate_episode_with_original_method(spine_events, episode_num, output_dir):
    """使用原有的generate_episodes.py方法生成剧集"""
    setup_paths()
    from modules.langchain_interface import call_llm

    # 准备生成剧集

    try:
        logger.info(f"Generating episode {episode_num} script with original method...")

        # 构建episode_structure（模拟原有格式）
        episode_structure = {
            "episode_number": episode_num,
            "title": f"第{episode_num}集",
            "main_plots": [event.get('text', '') for event in spine_events[:3]],
            "main_conflict": "现代思维与传统观念的冲突",
            "characters": ["罗兰", "安娜", "巴罗夫", "卡特"],
            "acts": {
                "opening": {"purpose": "建立情境", "scenes": 1},
                "development": {"purpose": "推进冲突", "scenes": 1},
                "climax_resolution": {"purpose": "解决冲突", "scenes": 1}
            }
        }

        # 构建prompt_data（按照原有格式）
        prompt_data = {
            "episode_structure": episode_structure,
            "global_outline": {"theme": "现代工程师穿越异世界的故事"},
            "previous_episode_script": "",
            "chapter_summaries": spine_events,
            "style": "快节奏",
            "language": "中文",
            "world_tone": "奇幻",
            "main_conflict": "现代思维与传统观念的冲突"
        }

        # 直接调用LLM
        response = call_llm(
            api_function="generate_full_script",
            prompt_data=prompt_data,
            using_cache=True
        )

        if response:
            # 保存原始响应
            script_file = output_dir / f"episode_{episode_num:02d}_script.txt"
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(response)

            # 转换为JSON格式
            episode_json = convert_script_to_json(response, episode_num)

            json_file = output_dir / f"episode_{episode_num:02d}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(episode_json, f, ensure_ascii=False, indent=2)

            logger.info(f"Episode {episode_num} generated successfully")
            return True
        else:
            logger.error(f"Failed to generate episode {episode_num}")
            return False

    except Exception as e:
        logger.error(f"Error generating episode {episode_num}: {e}")
        return False

def convert_script_to_json(script_text, episode_num):
    """将脚本文本转换为JSON格式"""

    # 解析脚本文本（改进版本）
    lines = script_text.split('\n')
    scenes = []
    current_scene = None
    dialogue = []
    scene_counter = 0

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 检测场景开始 - 更灵活的匹配
        if (line.startswith('【') and ('场景' in line or '·' in line)) or \
           line.startswith('——场景') or \
           (line.startswith('场景') and ('：' in line or '转至' in line)):

            # 保存上一个场景
            if current_scene and dialogue:
                current_scene['dialogue'] = dialogue
                scenes.append(current_scene)

            scene_counter += 1
            location = "边陲镇"
            if '地牢' in line:
                location = "地牢"
            elif '王宫' in line or '寝室' in line:
                location = "王宫"
            elif '书房' in line:
                location = "书房"
            elif '广场' in line:
                location = "广场"

            current_scene = {
                "scene_number": scene_counter,
                "n": scene_counter,
                "sn": scene_counter,
                "shot_type": "dialogue",
                "environment": {"image": location},
                "narration": {"nr": line},
                "sound_cues": []
            }
            dialogue = []

        # 检测对话 - 改进的对话识别
        elif '：' in line and not line.startswith('【'):
            parts = line.split('：', 1)  # 只分割第一个冒号
            if len(parts) == 2:
                speaker = parts[0].strip()
                text = parts[1].strip()

                # 处理特殊格式
                if speaker.startswith('——'):
                    continue  # 跳过场景分隔符

                # 确定情绪
                mood = "Neutral"
                if '（' in speaker and '）' in speaker:
                    # 提取情绪信息
                    mood_match = speaker[speaker.find('（')+1:speaker.find('）')]
                    if mood_match in ['低声', '急切', '迟疑', '压低声音', '小声']:
                        mood = "Worried"
                    elif mood_match in ['冷静', '平和', '坚定']:
                        mood = "Calm"
                    elif mood_match in ['心念', '内心', '心想']:
                        speaker = speaker.split('（')[0] + "（内心独白）"
                        mood = "Thoughtful"

                    # 清理speaker名称
                    speaker = speaker.split('（')[0]

                dialogue.append({
                    "c": speaker,
                    "m": mood,
                    "t": text
                })

        # 检测环境描述和特效
        elif line.startswith('【') and line.endswith('】'):
            dialogue.append({
                "c": "【环境音",
                "m": "Neutral",
                "t": line[1:-1]
            })
        elif line.startswith('——') and '安娜' in line and '火焰' in line:
            dialogue.append({
                "c": "【特效音",
                "m": "Neutral",
                "t": "火焰燃烧，金属熔化的声音"
            })

    # 添加最后一个场景
    if current_scene and dialogue:
        current_scene['dialogue'] = dialogue
        scenes.append(current_scene)

    # 如果没有解析到场景，创建默认场景
    if not scenes:
        default_dialogue = [
            {"c": "【旁白", "m": "Neutral", "t": f"第{episode_num}集开始"},
            {"c": "罗兰", "m": "Thoughtful", "t": "这个世界..."},
            {"c": "【环境音", "m": "Neutral", "t": "风声"}
        ]

        scenes = [{
            "scene_number": 1,
            "n": 1,
            "sn": 1,
            "shot_type": "dialogue",
            "environment": {"image": "边陲镇"},
            "narration": {"nr": f"第{episode_num}集"},
            "dialogue": default_dialogue,
            "sound_cues": []
        }]

    # 构建完整结构
    characters = [
        {"name": "罗兰", "gender": "male", "age": "Adult", "role": ["主角"], "aliases": []},
        {"name": "安娜", "gender": "female", "age": "Young", "role": ["女巫"], "aliases": []},
        {"name": "巴罗夫", "gender": "male", "age": "Adult", "role": ["助手"], "aliases": []},
        {"name": "卡特", "gender": "male", "age": "Adult", "role": ["骑士"], "aliases": []}
    ]

    return {
        "ep": {
            "ep_n": episode_num,
            "t": f"第{episode_num}集",
            "c": characters,
            "scenes": scenes
        },
        "episode_structure": {
            "episode_number": episode_num,
            "main_plots": ["主要情节"],
            "main_conflict": "核心冲突",
            "characters": [char["name"] for char in characters],
            "acts": {
                "opening": {"purpose": "开场", "scenes": 1},
                "development": {"purpose": "发展", "scenes": 1},
                "ending": {"purpose": "结尾", "scenes": 1}
            }
        }
    }

def main():
    try:
        # 创建输出目录
        output_dir = Path("./output_llm_simple")
        output_dir.mkdir(exist_ok=True)

        # 读取已生成的spine事件
        spine_file = Path("./output_llm_quick/spine_events.json")
        if not spine_file.exists():
            logger.error("Spine events file not found. Please run generate_llm_episodes_quick.py first.")
            return 1

        with open(spine_file, 'r', encoding='utf-8') as f:
            spine_results = json.load(f)

        logger.info(f"Loaded spine events for {len(spine_results)} chapters")

        # 生成第1集（章节1-4）
        episode_1_events = []
        for ch_num in range(1, 5):
            if str(ch_num) in spine_results:
                episode_1_events.extend(spine_results[str(ch_num)]["spine_events"])

        success_1 = generate_episode_with_original_method(episode_1_events, 1, output_dir)

        # 生成第2集（章节5-8）
        episode_2_events = []
        for ch_num in range(5, 9):
            if str(ch_num) in spine_results:
                episode_2_events.extend(spine_results[str(ch_num)]["spine_events"])

        success_2 = generate_episode_with_original_method(episode_2_events, 2, output_dir)

        logger.info("=" * 50)
        logger.info("SIMPLE LLM EPISODE GENERATION COMPLETED")
        logger.info("=" * 50)

        success_count = sum([success_1, success_2])
        logger.info(f"Generated {success_count} episodes successfully")
        logger.info(f"Output directory: {output_dir}")

        return 0 if success_count > 0 else 1

    except Exception as e:
        logger.error(f"Generation failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
