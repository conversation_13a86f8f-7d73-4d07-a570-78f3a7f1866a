#!/usr/bin/env python3
"""
Phase分配构建流水线
"""

import argparse
import logging
import sys
from typing import Dict, List, Any

from phase import PhaseAllocator
from load_save import load_json, save_json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def build_group_summaries_from_spine(spine_results: Dict[str, Any]) -> Dict[str, Any]:
    """从spine结果构建组摘要"""
    group_summaries = {}
    
    for chapter_id, spine_data in spine_results.items():
        chapter_num = spine_data["chapter_number"]
        spine_events = spine_data["spine_events"]
        
        # 计算张力分数（基于spine事件）
        tension_score = 0.0
        if spine_events:
            tension_scores = [event.get("tension", 0.0) for event in spine_events]
            tension_score = sum(tension_scores) / len(tension_scores)
        
        # 构建组摘要
        main_events = [event["text"] for event in spine_events]
        group_summary = " | ".join(main_events[:3])  # 前3个事件作为摘要
        
        # 估算token数（简化）
        token_count = len(group_summary) * 1.5
        
        group_summaries[f"g_{chapter_num:03d}"] = {
            "chapters": [chapter_num],
            "group_summary": group_summary,
            "main_events": main_events,
            "tension_score": tension_score,
            "token_count": token_count
        }
    
    return group_summaries

def main():
    parser = argparse.ArgumentParser(description="Build Phase Allocation from Spine Events")
    parser.add_argument("spine_file", help="Input spine events JSON file")
    parser.add_argument("--output", default="phase_allocation.json", help="Output file for phase allocation")
    parser.add_argument("--debug", action="store_true", help="Save debug information")
    
    args = parser.parse_args()
    
    try:
        # 加载spine事件
        logger.info(f"Loading spine events from {args.spine_file}")
        spine_results = load_json(args.spine_file)
        
        if not spine_results:
            logger.error("No spine events found in input file")
            return 1
        
        logger.info(f"Loaded spine events for {len(spine_results)} chapters")
        
        # 构建组摘要
        logger.info("Building group summaries from spine events...")
        group_summaries = build_group_summaries_from_spine(spine_results)
        
        # 初始化Phase分配器
        phase_allocator = PhaseAllocator()
        
        # 执行动态分配
        logger.info("Executing dynamic phase allocation...")
        allocation_result = phase_allocator.allocate_phases_dynamic(group_summaries)
        
        # 提取结果
        allocation = allocation_result["allocation"]
        debug_report = allocation_result["debug_report"]
        validation_issues = allocation_result["validation_issues"]
        weights = allocation_result["weights"]
        
        # 输出分配结果
        logger.info("Phase allocation completed:")
        for phase, groups in allocation.to_dict().items():
            logger.info(f"  {phase}: {len(groups)} groups - {groups}")
        
        if validation_issues:
            logger.warning("Validation issues found:")
            for issue in validation_issues:
                logger.warning(f"  - {issue}")
        
        # 保存主要结果
        output_data = {
            "allocation": allocation.to_dict(),
            "weights": weights,
            "validation_issues": validation_issues,
            "group_summaries": group_summaries
        }
        
        save_json(output_data, args.output)
        logger.info(f"Saved phase allocation to {args.output}")
        
        # 保存调试信息（可选）
        if args.debug:
            debug_file = args.output.replace('.json', '_debug.json')
            save_json(debug_report, debug_file)
            logger.info(f"Saved debug information to {debug_file}")
        
        # 输出统计信息
        total_groups = sum(len(groups) for groups in allocation.to_dict().values())
        logger.info(f"Allocation statistics:")
        logger.info(f"  Total groups: {total_groups}")
        
        for phase, groups in allocation.to_dict().items():
            ratio = len(groups) / total_groups if total_groups > 0 else 0
            target_ratio = phase_allocator.target_ratios.get(phase, 0)
            logger.info(f"  {phase}: {len(groups)} groups ({ratio:.1%}, target: {target_ratio:.1%})")
        
        return 0
        
    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
