# Novel Rewrite Pipeline Summary

**Input File**: output_rewrite/converted_input.json
**Output Directory**: ./output_rewrite
**Compression Ratio**: 0.3

## Results
- **Total Episodes Generated**: 2
- **Quality Distribution**:
  - Green: 0
  - Yellow: 0
  - Red: 2

## Output Files
- Spine Events: `spine_events.json`
- Phase Allocation: `phase_allocation.json`
- Episodes: `episodes.json`
- Individual Scripts: `episode_*.txt`

## Next Steps
1. Review quality reports for any Yellow/Red episodes
2. Manual review and editing if needed
3. Proceed to video production pipeline

*Generated by novel_rewrite pipeline*