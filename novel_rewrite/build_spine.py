#!/usr/bin/env python3
"""
Spine事件构建流水线
"""

import argparse
import logging
import sys
from typing import Dict, List, Any

from spine import SpineExtractor
from alias import AliasManager
from load_save import load_json, save_json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    parser = argparse.ArgumentParser(description="Build Spine Events from Chapter Summaries")
    parser.add_argument("input_file", help="Input JSON file with chapter summaries")
    parser.add_argument("--output", default="spine_events.json", help="Output file for spine events")
    parser.add_argument("--alias_map", default="alias_map_seed.json", help="Alias map file")
    parser.add_argument("--max_events", type=int, default=4, help="Max events per chapter")
    parser.add_argument("--auto_alias", action="store_true", help="Auto-discover aliases")
    
    args = parser.parse_args()
    
    try:
        # 加载输入数据
        logger.info(f"Loading chapter summaries from {args.input_file}")
        input_data = load_json(args.input_file)
        chapter_summaries = input_data.get("chapters", [])
        
        if not chapter_summaries:
            logger.error("No chapters found in input data")
            return 1
        
        logger.info(f"Loaded {len(chapter_summaries)} chapters")
        
        # 初始化别名管理器
        alias_manager = AliasManager(args.alias_map)
        
        # 自动发现别名（可选）
        if args.auto_alias:
            logger.info("Auto-discovering aliases...")
            texts = [ch.get('narrative', {}).get('content', '') for ch in chapter_summaries]
            discovered = alias_manager.auto_discover_aliases(texts)
            
            if discovered:
                logger.info(f"Discovered {len(discovered)} alias groups")
                for canonical, aliases in discovered.items():
                    for alias in aliases:
                        alias_manager.add_alias(canonical, alias)
                alias_manager.save_alias_map()
        
        # 模拟LLM客户端
        class MockLLMClient:
            def call_json_response(self, prompt, expected_fields=None):
                # 简单的模拟响应
                return {
                    "spine_events": [
                        {
                            "id": "1-1",
                            "type": "plot",
                            "text": "关键事件描述",
                            "importance": 0.8,
                            "tension": 0.6
                        }
                    ]
                }
        
        # 初始化Spine抽取器
        llm_client = MockLLMClient()
        spine_extractor = SpineExtractor(llm_client, alias_manager.alias_map)
        
        # 处理每个章节
        spine_results = {}
        total_events = 0
        
        for chapter_summary in chapter_summaries:
            chapter_num = chapter_summary.get('basic_info', {}).get('chapter_number')
            if chapter_num is None:
                logger.warning("Chapter number not found, skipping")
                continue
            
            logger.info(f"Processing chapter {chapter_num}")
            
            # 抽取Spine事件
            spine_events = spine_extractor.extract_spine_events(chapter_summary, args.max_events)
            
            # 保存结果
            spine_results[str(chapter_num)] = {
                "chapter_number": chapter_num,
                "spine_events": [event.to_dict() for event in spine_events],
                "event_count": len(spine_events)
            }
            
            total_events += len(spine_events)
            logger.info(f"Chapter {chapter_num}: extracted {len(spine_events)} spine events")
        
        # 保存结果
        save_json(spine_results, args.output)
        logger.info(f"Saved spine events to {args.output}")
        
        # 输出统计信息
        logger.info(f"Processing completed:")
        logger.info(f"  Total chapters: {len(spine_results)}")
        logger.info(f"  Total spine events: {total_events}")
        logger.info(f"  Average events per chapter: {total_events/len(spine_results):.1f}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
