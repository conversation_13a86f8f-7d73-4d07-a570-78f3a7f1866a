"""
剧情漂移检测器
"""

import re
import numpy as np
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class QualityReport:
    """质量报告"""
    episode_num: int
    divergence_score: float
    threshold: float
    status: str  # "green" | "yellow" | "red"
    missing_spine_events: List[str]
    contradicted_events: List[str]
    suggestions: List[str]
    severity: str  # "LOW" | "MEDIUM" | "HIGH"
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "episode_num": self.episode_num,
            "divergence_score": self.divergence_score,
            "threshold": self.threshold,
            "status": self.status,
            "missing_spine_events": self.missing_spine_events,
            "contradicted_events": self.contradicted_events,
            "suggestions": self.suggestions,
            "severity": self.severity,
            "timestamp": self.timestamp.isoformat()
        }

class DriftDetector:
    """剧情漂移检测器"""
    
    def __init__(self, vector_cache=None):
        self.vector_cache = vector_cache
        self.score_history = []
        
        # 模拟向量编码器（实际应该使用真实的模型）
        self.mock_encoder = True
    
    def extract_script_events(self, script_text: str) -> List[str]:
        """从生成的剧本中抽取关键事件"""
        events = []
        
        # 方法1: 规则匹配 <人名><动词><关键宾语>
        rule_events = self._extract_events_by_rules(script_text)
        events.extend(rule_events)
        
        # 方法2: 如果规则匹配结果不足，使用简单的句子分割
        if len(events) < 3:
            fallback_events = self._extract_events_fallback(script_text)
            events.extend(fallback_events)
        
        # 去重和清理
        events = list(set(events))
        events = [event.strip() for event in events if len(event.strip()) > 5]
        
        return events[:10]  # 最多返回10个事件
    
    def _extract_events_by_rules(self, script_text: str) -> List[str]:
        """使用规则抽取事件"""
        events = []
        
        # 中文动作句模式
        patterns = [
            r'([一-龯]{2,4})(决定|选择|发现|揭露|攻击|保护|背叛|拯救|失败|成功|死亡|离开|到达)([一-龯\s]{2,20})',
            r'([一-龯]{2,4})(对|向|朝)([一-龯]{2,4})(说|道|喊|问|答|承认|宣布)([一-龯\s]{2,30})',
            r'([一-龯]{2,4})(看到|听到|感受到|意识到)([一-龯\s]{2,20})',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, script_text)
            for match in matches:
                if isinstance(match, tuple):
                    event = ''.join(match).strip()
                else:
                    event = match.strip()
                
                if len(event) > 5:
                    events.append(event)
        
        return events
    
    def _extract_events_fallback(self, script_text: str) -> List[str]:
        """兜底事件抽取"""
        sentences = re.split(r'[。！？]', script_text)
        
        # 筛选包含动作的句子
        action_keywords = ['决定', '选择', '发现', '攻击', '保护', '说', '道', '看到', '听到']
        events = []
        
        for sentence in sentences:
            if any(keyword in sentence for keyword in action_keywords):
                if 10 <= len(sentence) <= 50:  # 长度合适的句子
                    events.append(sentence.strip())
        
        return events[:5]
    
    def calculate_divergence_score(self, script_events: List[str], spine_events: List[str]) -> float:
        """计算剧情漂移分数"""
        if not script_events or not spine_events:
            return 1.0  # 完全漂移
        
        try:
            # 模拟向量相似度计算
            if self.mock_encoder:
                return self._mock_similarity_calculation(script_events, spine_events)
            
            # 实际的向量计算逻辑
            script_vectors = self._encode_events(script_events)
            spine_vectors = self._encode_events(spine_events)
            
            # 计算相似度矩阵
            similarities = self._compute_similarity_matrix(script_vectors, spine_vectors)
            
            # 对每个脚本事件，找到最相似的spine事件
            max_similarities = np.max(similarities, axis=1)
            
            # 计算平均相似度
            avg_similarity = np.mean(max_similarities)
            
            # 漂移分数 = 1 - 相似度
            divergence_score = 1.0 - avg_similarity
            
            return float(np.clip(divergence_score, 0.0, 1.0))
            
        except Exception as e:
            logger.error(f"Failed to calculate divergence score: {e}")
            return 0.5  # 默认中等漂移
    
    def _mock_similarity_calculation(self, script_events: List[str], spine_events: List[str]) -> float:
        """模拟相似度计算"""
        # 简单的关键词匹配相似度
        total_similarity = 0.0
        
        for script_event in script_events:
            max_sim = 0.0
            for spine_event in spine_events:
                # 计算关键词重叠度
                script_words = set(re.findall(r'[一-龯]{2,4}', script_event))
                spine_words = set(re.findall(r'[一-龯]{2,4}', spine_event))
                
                if script_words and spine_words:
                    overlap = len(script_words & spine_words)
                    union = len(script_words | spine_words)
                    similarity = overlap / union if union > 0 else 0
                    max_sim = max(max_sim, similarity)
            
            total_similarity += max_sim
        
        avg_similarity = total_similarity / len(script_events) if script_events else 0
        return 1.0 - avg_similarity
    
    def _encode_events(self, events: List[str]) -> np.ndarray:
        """编码事件为向量（占位符）"""
        # 这里应该使用真实的向量编码器
        vectors = []
        for event in events:
            # 模拟768维向量
            vector = np.random.rand(768)
            vectors.append(vector)
        return np.array(vectors)
    
    def _compute_similarity_matrix(self, vectors1: np.ndarray, vectors2: np.ndarray) -> np.ndarray:
        """计算相似度矩阵"""
        # 归一化向量
        vectors1_norm = vectors1 / np.linalg.norm(vectors1, axis=1, keepdims=True)
        vectors2_norm = vectors2 / np.linalg.norm(vectors2, axis=1, keepdims=True)
        
        # 计算余弦相似度
        similarity_matrix = np.dot(vectors1_norm, vectors2_norm.T)
        
        return similarity_matrix
    
    def get_dynamic_threshold(self, score_history: Optional[List[float]] = None) -> float:
        """获取动态阈值"""
        if score_history is None:
            score_history = self.score_history
        
        if len(score_history) < 3:
            return 0.45  # 默认阈值
        
        # 使用最近5个分数
        recent_scores = score_history[-5:]
        
        # 使用median + IQR计算动态阈值
        robust_mu = np.median(recent_scores)
        q75 = np.percentile(recent_scores, 75)
        q25 = np.percentile(recent_scores, 25)
        iqr = q75 - q25 + 1e-5
        
        # 动态阈值
        dynamic_threshold = robust_mu + 1.5 * iqr
        
        # 限制在合理范围内
        return float(np.clip(dynamic_threshold, 0.45, 0.55))
    
    def detect_drift(self, script_text: str, spine_events: List[str], episode_num: int) -> QualityReport:
        """综合检测剧情漂移"""
        # 抽取脚本事件
        script_events = self.extract_script_events(script_text)
        
        # 计算漂移分数
        divergence_score = self.calculate_divergence_score(script_events, spine_events)
        
        # 更新历史记录
        self.score_history.append(divergence_score)
        
        # 获取动态阈值
        threshold = self.get_dynamic_threshold()
        
        # 确定状态
        if divergence_score > threshold:
            status = "yellow" if divergence_score < 0.7 else "red"
        else:
            status = "green"
        
        # 分析具体问题
        missing_events, contradicted_events = self._analyze_specific_issues(script_events, spine_events)
        
        # 生成建议
        suggestions = self._generate_suggestions(missing_events, contradicted_events)
        
        # 确定严重程度
        severity = self._determine_severity(divergence_score, missing_events, contradicted_events)
        
        return QualityReport(
            episode_num=episode_num,
            divergence_score=divergence_score,
            threshold=threshold,
            status=status,
            missing_spine_events=missing_events,
            contradicted_events=contradicted_events,
            suggestions=suggestions,
            severity=severity,
            timestamp=datetime.now()
        )
    
    def _analyze_specific_issues(self, script_events: List[str], spine_events: List[str]) -> Tuple[List[str], List[str]]:
        """分析具体的缺失和矛盾事件"""
        missing_events = []
        contradicted_events = []
        
        # 简化版本：基于关键词匹配
        spine_keywords = set()
        for spine_event in spine_events:
            # 提取关键词
            keywords = re.findall(r'[一-龯]{2,4}', spine_event)
            spine_keywords.update(keywords)
        
        script_keywords = set()
        for script_event in script_events:
            keywords = re.findall(r'[一-龯]{2,4}', script_event)
            script_keywords.update(keywords)
        
        # 检查缺失的关键概念
        missing_keywords = spine_keywords - script_keywords
        if missing_keywords:
            missing_events = [f"缺失关键概念: {', '.join(list(missing_keywords)[:3])}"]
        
        return missing_events, contradicted_events
    
    def _generate_suggestions(self, missing_events: List[str], contradicted_events: List[str]) -> List[str]:
        """生成修改建议"""
        suggestions = []
        
        if missing_events:
            suggestions.append(f"补充缺失事件: {missing_events[0]}")
        
        if contradicted_events:
            suggestions.append(f"修正矛盾内容: {contradicted_events[0]}")
        
        if not suggestions:
            suggestions.append("内容基本符合要求，可考虑微调细节")
        
        return suggestions
    
    def _determine_severity(self, divergence_score: float, missing_events: List[str], contradicted_events: List[str]) -> str:
        """确定严重程度"""
        if divergence_score > 0.7 or len(missing_events) > 2:
            return "HIGH"
        elif divergence_score > 0.5 or len(missing_events) > 0:
            return "MEDIUM"
        else:
            return "LOW"
