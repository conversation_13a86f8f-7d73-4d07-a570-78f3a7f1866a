# 剧集结构生成提示词

## 任务描述
根据Spine事件和Phase分配，生成详细的剧集结构，为剧本编写提供框架。

## 输入信息
- Spine事件列表（必须覆盖）
- 前一集的结束状态
- 压缩比参数
- 目标时长

## 输出结构

### 1. 剧集基本信息
```json
{
  "episode_number": 1,
  "total_scenes": 5,
  "estimated_duration": 12.0,
  "compression_ratio": 0.3,
  "spine_coverage": ["事件ID列表"]
}
```

### 2. 场景结构
```json
{
  "scenes": [
    {
      "scene_number": 1,
      "title": "场景标题",
      "location": "具体地点描述",
      "time": "时间设定（相对或绝对）",
      "characters": ["主要角色列表"],
      "main_conflict": "本场景的核心冲突或推进点",
      "emotional_arc": "角色情感变化描述",
      "key_events": ["本场景发生的关键事件"],
      "spine_events_covered": ["覆盖的spine事件ID"],
      "transition": "与下一场景的转换方式",
      "estimated_duration": 2.5
    }
  ]
}
```

### 3. 结束状态
```json
{
  "end_state": {
    "char_states": {
      "角色名": {
        "goal": "当前目标（≤10字）",
        "risk": "面临风险（≤10字）",
        "emotion": "情感状态（≤10字）"
      }
    },
    "open_hooks": ["本集结束时的未解悬念"],
    "world_delta": ["本集产生的世界变化"]
  }
}
```

## 设计原则

### 1. Spine事件覆盖
- **强制约束**: 所有列出的spine_events必须在剧集中出现
- **保持原意**: 不能改变事件的核心结果和因果关系
- **合理分布**: 事件要在合适的场景中自然呈现

### 2. 结构完整性
- **起承转合**: 每集要有完整的故事弧线
- **节奏控制**: 根据压缩比调整内容密度
- **转场自然**: 场景间要有合理的过渡

### 3. 连续性保持
- **状态继承**: 与前一集的结束状态保持逻辑连续性
- **角色一致**: 角色行为和动机要符合前面的设定
- **世界观统一**: 不能引入与设定矛盾的元素

## 场景设计要求

### 开场场景（场景1）
- 快速建立当前情境
- 回应前集留下的悬念
- 设定本集的主要冲突

### 发展场景（场景2-4）
- 逐步推进spine事件
- 保持张力递增
- 深化角色关系

### 高潮场景（场景5）
- 处理本集的主要冲突
- 实现重要的情节转折
- 体现角色成长

### 结尾场景（场景6，可选）
- 解决部分问题
- 设置新的悬念
- 为下一集做铺垫

## 时长控制

### 压缩比指导
- **0.3**: 保留大部分细节，适度压缩
- **0.5**: 平衡压缩，专注核心情节
- **0.7**: 高度压缩，快节奏推进

### 场景时长分配
- 开场: 2-3分钟
- 发展: 2.5-3分钟/场景
- 高潮: 3-4分钟
- 结尾: 1.5-2分钟

## 质量检查

### 必检项目
- [ ] 所有spine事件都被覆盖
- [ ] 与前一集状态连续性合理
- [ ] 场景转换自然流畅
- [ ] 角色发展符合逻辑
- [ ] 总时长在目标范围内
- [ ] 没有引入原著外的元素

### 优化建议
- 确保每个场景都推进整体剧情
- 对话要有层次，体现角色关系变化
- 场景结尾要有适当的转场设置
- 整集要有完整的起承转合
