#!/usr/bin/env python3
"""
将novel_rewrite的提示词集成到主项目的提示词系统中
"""

import os
import sys
from pathlib import Path

def integrate_prompts():
    """集成提示词到主项目"""
    
    # 获取路径
    novel_rewrite_dir = Path(__file__).parent
    main_project_dir = novel_rewrite_dir.parent
    modules_dir = main_project_dir / "modules"
    
    # 检查modules目录是否存在
    if not modules_dir.exists():
        print(f"错误: modules目录不存在: {modules_dir}")
        return False
    
    # 读取novel_rewrite的提示词
    prompts_file = novel_rewrite_dir / "prompts_novel_rewrite.py"
    if not prompts_file.exists():
        print(f"错误: 提示词文件不存在: {prompts_file}")
        return False
    
    # 导入novel_rewrite提示词
    sys.path.insert(0, str(novel_rewrite_dir))
    try:
        from prompts_novel_rewrite import novel_rewrite_prompts, novel_rewrite_system_messages
    except ImportError as e:
        print(f"错误: 无法导入novel_rewrite提示词: {e}")
        return False
    
    # 检查是否需要创建新的提示词文件
    target_prompts_file = modules_dir / "prompts_novel_rewrite.py"
    
    # 创建提示词文件内容
    content = f'''# modules/prompts_novel_rewrite.py
"""
Novel Rewrite系统的提示词模板 - 自动生成
"""

# 系统消息
novel_rewrite_system_messages = {repr(novel_rewrite_system_messages)}

# 提示词模板  
novel_rewrite_prompts = {repr(novel_rewrite_prompts)}
'''
    
    # 写入文件
    try:
        with open(target_prompts_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"成功创建提示词文件: {target_prompts_file}")
    except Exception as e:
        print(f"错误: 无法写入提示词文件: {e}")
        return False
    
    # 更新gpt_parameters.py以包含novel_rewrite提示词
    gpt_params_file = modules_dir / "gpt_parameters.py"
    if gpt_params_file.exists():
        try:
            # 读取现有内容
            with open(gpt_params_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经包含novel_rewrite导入
            if "from modules.prompts_novel_rewrite import" not in content:
                # 找到其他提示词导入的位置
                import_line = "from modules.prompts_translation import translation_prompts, translation_system_messages"
                if import_line in content:
                    new_import = "from modules.prompts_novel_rewrite import novel_rewrite_prompts, novel_rewrite_system_messages"
                    content = content.replace(import_line, f"{import_line}\n{new_import}")
                    
                    # 添加到PROMPT_COLLECTIONS
                    collections_pattern = "'translation': {\n        'prompts': translation_prompts,\n        'system_messages': translation_system_messages\n    }"
                    if collections_pattern in content:
                        new_collection = """'novel_rewrite': {
        'prompts': novel_rewrite_prompts,
        'system_messages': novel_rewrite_system_messages
    }"""
                        content = content.replace(collections_pattern, f"{collections_pattern},\n    {new_collection}")
                    
                    # 写回文件
                    with open(gpt_params_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"成功更新gpt_parameters.py")
                else:
                    print("警告: 无法找到合适的位置插入novel_rewrite导入")
            else:
                print("gpt_parameters.py已经包含novel_rewrite导入")
                
        except Exception as e:
            print(f"错误: 无法更新gpt_parameters.py: {e}")
            return False
    else:
        print(f"警告: gpt_parameters.py不存在: {gpt_params_file}")
    
    # 添加GPT参数配置
    try:
        with open(gpt_params_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含novel_rewrite参数
        if '"extract_spine_events"' not in content:
            # 找到GPT_PARAMETERS的位置
            params_start = content.find("GPT_PARAMETERS = {")
            if params_start != -1:
                # 找到第一个参数的位置
                first_param_start = content.find('"', params_start + len("GPT_PARAMETERS = {"))
                if first_param_start != -1:
                    # 插入novel_rewrite参数
                    novel_rewrite_params = '''    # Novel Rewrite相关
    "extract_spine_events": {
        "temperature": 0.3,
        "top_p": 0.8,
        "frequency_penalty": 0.3,
        "presence_penalty": 0.2
    },
    "generate_episode_structure": {
        "temperature": 0.4,
        "top_p": 0.9,
        "frequency_penalty": 0.3,
        "presence_penalty": 0.2
    },
    "generate_full_script": {
        "temperature": 0.7,
        "top_p": 0.95,
        "frequency_penalty": 0.4,
        "presence_penalty": 0.3
    },

    '''
                    content = content[:first_param_start] + novel_rewrite_params + content[first_param_start:]
                    
                    with open(gpt_params_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print("成功添加novel_rewrite参数配置")
                else:
                    print("警告: 无法找到GPT_PARAMETERS的参数位置")
            else:
                print("警告: 无法找到GPT_PARAMETERS定义")
        else:
            print("GPT_PARAMETERS已经包含novel_rewrite参数")
            
    except Exception as e:
        print(f"错误: 无法添加GPT参数配置: {e}")
        return False
    
    print("提示词集成完成!")
    return True

if __name__ == "__main__":
    success = integrate_prompts()
    sys.exit(0 if success else 1)
