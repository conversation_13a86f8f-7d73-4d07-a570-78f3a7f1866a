"""
角色别名管理器
"""

import re
import json
import logging
from typing import Dict, List, Set, Optional
from collections import defaultdict

logger = logging.getLogger(__name__)

class AliasManager:
    """角色别名管理器"""
    
    def __init__(self, alias_map_path: str = "alias_map_seed.json"):
        self.alias_map_path = alias_map_path
        self.alias_map = self._load_alias_map()
        self.reverse_map = self._build_reverse_map()
        
    def _load_alias_map(self) -> Dict[str, List[str]]:
        """加载别名映射"""
        try:
            with open(self.alias_map_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"Alias map not found at {self.alias_map_path}, creating empty map")
            return {}
    
    def _build_reverse_map(self) -> Dict[str, str]:
        """构建反向映射：别名 -> 标准名"""
        reverse_map = {}
        for canonical, aliases in self.alias_map.items():
            # 标准名映射到自己
            reverse_map[canonical] = canonical
            # 别名映射到标准名
            for alias in aliases:
                reverse_map[alias] = canonical
        return reverse_map
    
    def get_canonical_name(self, name: str) -> str:
        """获取标准名称"""
        return self.reverse_map.get(name, name)
    
    def add_alias(self, canonical: str, alias: str) -> None:
        """添加别名"""
        if canonical not in self.alias_map:
            self.alias_map[canonical] = []
        
        if alias not in self.alias_map[canonical]:
            self.alias_map[canonical].append(alias)
            self.reverse_map[alias] = canonical
            logger.info(f"Added alias '{alias}' for '{canonical}'")
    
    def standardize_text(self, text: str) -> str:
        """标准化文本中的角色名称"""
        result = text
        
        # 按长度排序，先替换长的别名，避免部分匹配问题
        sorted_aliases = sorted(self.reverse_map.keys(), key=len, reverse=True)
        
        for alias in sorted_aliases:
            canonical = self.reverse_map[alias]
            if alias != canonical:  # 只替换别名，不替换标准名
                # 使用词边界匹配，避免部分替换
                pattern = r'\b' + re.escape(alias) + r'\b'
                result = re.sub(pattern, canonical, result)
        
        return result
    
    def extract_character_names(self, text: str) -> Set[str]:
        """从文本中提取角色名称"""
        characters = set()
        
        # 检查已知的所有名称（标准名和别名）
        for name in self.reverse_map.keys():
            pattern = r'\b' + re.escape(name) + r'\b'
            if re.search(pattern, text):
                canonical = self.get_canonical_name(name)
                characters.add(canonical)
        
        return characters
    
    def auto_discover_aliases(self, texts: List[str], min_frequency: int = 3) -> Dict[str, List[str]]:
        """自动发现可能的别名"""
        # 提取所有可能的人名
        potential_names = self._extract_potential_names(texts)
        
        # 统计频率
        name_frequency = defaultdict(int)
        for text in texts:
            for name in potential_names:
                if name in text:
                    name_frequency[name] += 1
        
        # 过滤低频名称
        frequent_names = {name for name, freq in name_frequency.items() if freq >= min_frequency}
        
        # 基于相似度聚类
        potential_aliases = self._cluster_similar_names(frequent_names)
        
        return potential_aliases
    
    def _extract_potential_names(self, texts: List[str]) -> Set[str]:
        """提取潜在的人名"""
        potential_names = set()
        
        # 中文人名模式
        chinese_name_patterns = [
            r'[王李张刘陈杨黄赵吴周徐孙马朱胡郭何高林罗郑梁谢宋唐许韩冯邓曹彭曾萧田董袁潘于蒋蔡余杜叶程苏魏吕丁任沈姚卢姜崔钟谭陆汪范金石廖贾夏韦付方白邹孟熊秦邱江尹薛闫段雷侯龙史陶黎贺顾毛郝龚邵万钱严覃武戴莫孔向汤][一-龯]{1,2}',
            r'[一-龯]{2,3}(?=说|道|想|看|听|问|答|笑|哭|叫|喊)',
        ]
        
        for text in texts:
            for pattern in chinese_name_patterns:
                matches = re.findall(pattern, text)
                potential_names.update(matches)
        
        return potential_names
    
    def _cluster_similar_names(self, names: Set[str]) -> Dict[str, List[str]]:
        """基于相似度聚类名称"""
        clusters = {}
        processed = set()
        
        for name in names:
            if name in processed:
                continue
                
            # 找到相似的名称
            similar_names = []
            for other_name in names:
                if other_name != name and other_name not in processed:
                    if self._are_names_similar(name, other_name):
                        similar_names.append(other_name)
            
            if similar_names:
                # 选择最长的作为标准名
                all_names = [name] + similar_names
                canonical = max(all_names, key=len)
                aliases = [n for n in all_names if n != canonical]
                
                clusters[canonical] = aliases
                processed.update(all_names)
            else:
                processed.add(name)
        
        return clusters
    
    def _are_names_similar(self, name1: str, name2: str) -> bool:
        """判断两个名称是否相似"""
        # 简单的相似度判断
        # 1. 包含关系
        if name1 in name2 or name2 in name1:
            return True
        
        # 2. 共同字符比例
        common_chars = set(name1) & set(name2)
        min_len = min(len(name1), len(name2))
        if len(common_chars) / min_len > 0.6:
            return True
        
        return False
    
    def save_alias_map(self) -> None:
        """保存别名映射到文件"""
        with open(self.alias_map_path, 'w', encoding='utf-8') as f:
            json.dump(self.alias_map, f, ensure_ascii=False, indent=2)
        logger.info(f"Saved alias map to {self.alias_map_path}")
    
    def get_statistics(self) -> Dict[str, int]:
        """获取别名映射统计信息"""
        return {
            "total_canonical_names": len(self.alias_map),
            "total_aliases": sum(len(aliases) for aliases in self.alias_map.values()),
            "total_mappings": len(self.reverse_map)
        }
