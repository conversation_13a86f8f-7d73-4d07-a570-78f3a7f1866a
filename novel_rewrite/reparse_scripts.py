#!/usr/bin/env python3
"""
重新解析已生成的剧本文本，转换为正确的JSON格式
"""

import json
import sys
import os
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_script_to_json(script_text, episode_num):
    """将脚本文本解析为JSON格式"""
    
    lines = script_text.split('\n')
    scenes = []
    current_scene = None
    dialogue = []
    scene_counter = 0
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('———') or line.startswith('【第') or line == '【剧终钩子】':
            continue
            
        # 检测场景开始
        if (line.startswith('【') and ('·' in line or '场景' in line)) or \
           line.startswith('——场景') or \
           (line.startswith('广场') or line.startswith('王宫') or line.startswith('地牢')):
            
            # 保存上一个场景
            if current_scene and dialogue:
                current_scene['dialogue'] = dialogue
                scenes.append(current_scene)
            
            scene_counter += 1
            location = "边陲镇"
            
            # 确定场景位置
            if '地牢' in line:
                location = "地牢"
            elif '王宫' in line or '寝室' in line or '寝宫' in line:
                location = "王宫"
            elif '书房' in line:
                location = "书房"
            elif '广场' in line or '刑场' in line:
                location = "广场"
            elif '城堡' in line:
                location = "城堡"
            elif '实验室' in line:
                location = "实验室"
            
            current_scene = {
                "scene_number": scene_counter,
                "n": scene_counter,
                "sn": scene_counter,
                "shot_type": "dialogue",
                "environment": {"image": location},
                "narration": {"nr": line},
                "sound_cues": []
            }
            dialogue = []
            continue
            
        # 检测对话
        if '：' in line and not line.startswith('【'):
            parts = line.split('：', 1)
            if len(parts) == 2:
                speaker = parts[0].strip()
                text = parts[1].strip()
                
                # 跳过场景分隔符
                if speaker.startswith('——'):
                    continue
                
                # 确定情绪
                mood = "Neutral"
                original_speaker = speaker
                
                if '（' in speaker and '）' in speaker:
                    mood_text = speaker[speaker.find('（')+1:speaker.find('）')]
                    speaker = speaker.split('（')[0].strip()
                    
                    # 映射情绪
                    mood_mapping = {
                        '低声': 'Worried',
                        '急切': 'Urgent', 
                        '迟疑': 'Hesitant',
                        '压低声音': 'Worried',
                        '小声': 'Quiet',
                        '冷静': 'Calm',
                        '平和': 'Peaceful',
                        '坚定': 'Determined',
                        '心念': 'Thoughtful',
                        '内心': 'Thoughtful',
                        '心想': 'Thoughtful',
                        '斩钉截铁': 'Determined',
                        '打断': 'Interrupting',
                        '警告': 'Warning',
                        '不悦': 'Displeased',
                        '带刺': 'Sarcastic',
                        '虚弱': 'Weak',
                        '喉咙哽咽': 'Emotional',
                        '颤抖': 'Trembling',
                        '仓促进来': 'Rushed',
                        '盯着': 'Focused'
                    }
                    
                    mood = mood_mapping.get(mood_text, 'Neutral')
                    
                    # 处理内心独白
                    if mood_text in ['心念', '内心', '心想']:
                        speaker = speaker + "（内心独白）"
                
                dialogue.append({
                    "c": speaker,
                    "m": mood,
                    "t": text
                })
        
        # 检测环境描述和特效
        elif line.startswith('【') and line.endswith('】'):
            content = line[1:-1]
            if '场景' in content:
                continue  # 跳过场景标记
            dialogue.append({
                "c": "【环境音",
                "m": "Neutral",
                "t": content
            })
        elif line.startswith('——') and ('安娜' in line or '火焰' in line):
            dialogue.append({
                "c": "【特效音",
                "m": "Neutral",
                "t": "火焰燃烧，金属熔化的声音"
            })
        elif line.startswith('（') and line.endswith('）') and '内心独白' in line:
            dialogue.append({
                "c": "【旁白",
                "m": "Neutral",
                "t": line[1:-1]
            })
    
    # 添加最后一个场景
    if current_scene and dialogue:
        current_scene['dialogue'] = dialogue
        scenes.append(current_scene)
    
    # 如果没有解析到场景，创建单个场景包含所有对话
    if not scenes and dialogue:
        scenes = [{
            "scene_number": 1,
            "n": 1,
            "sn": 1,
            "shot_type": "dialogue",
            "environment": {"image": "边陲镇"},
            "narration": {"nr": f"第{episode_num}集"},
            "dialogue": dialogue,
            "sound_cues": []
        }]
    
    # 构建完整结构
    characters = [
        {"name": "罗兰", "gender": "male", "age": "Adult", "role": ["主角"], "aliases": []},
        {"name": "安娜", "gender": "female", "age": "Young", "role": ["女巫"], "aliases": []},
        {"name": "巴罗夫", "gender": "male", "age": "Adult", "role": ["助手"], "aliases": []},
        {"name": "卡特", "gender": "male", "age": "Adult", "role": ["骑士"], "aliases": []}
    ]
    
    return {
        "ep": {
            "ep_n": episode_num,
            "t": f"第{episode_num}集",
            "c": characters,
            "scenes": scenes
        },
        "episode_structure": {
            "episode_number": episode_num,
            "main_plots": ["穿越与身份认知", "女巫安娜的救赎", "传统与现代的冲突"],
            "main_conflict": "现代思维与传统观念的冲突",
            "characters": [char["name"] for char in characters],
            "acts": {
                "opening": {"purpose": "建立情境和冲突", "scenes": len(scenes)//3 or 1},
                "development": {"purpose": "推进主要情节", "scenes": len(scenes)//3 or 1},
                "ending": {"purpose": "解决冲突并铺垫", "scenes": len(scenes)//3 or 1}
            }
        }
    }

def main():
    try:
        output_dir = Path("./output_llm_simple")
        
        # 重新解析第1集
        script_1_file = output_dir / "episode_01_script.txt"
        if script_1_file.exists():
            logger.info("Reparsing episode 1...")
            with open(script_1_file, 'r', encoding='utf-8') as f:
                script_1_text = f.read()
            
            episode_1_json = parse_script_to_json(script_1_text, 1)
            
            with open(output_dir / "episode_01.json", 'w', encoding='utf-8') as f:
                json.dump(episode_1_json, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Episode 1: {len(episode_1_json['ep']['scenes'])} scenes, "
                       f"{sum(len(scene['dialogue']) for scene in episode_1_json['ep']['scenes'])} dialogue lines")
        
        # 重新解析第2集
        script_2_file = output_dir / "episode_02_script.txt"
        if script_2_file.exists():
            logger.info("Reparsing episode 2...")
            with open(script_2_file, 'r', encoding='utf-8') as f:
                script_2_text = f.read()
            
            episode_2_json = parse_script_to_json(script_2_text, 2)
            
            with open(output_dir / "episode_02.json", 'w', encoding='utf-8') as f:
                json.dump(episode_2_json, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Episode 2: {len(episode_2_json['ep']['scenes'])} scenes, "
                       f"{sum(len(scene['dialogue']) for scene in episode_2_json['ep']['scenes'])} dialogue lines")
        
        logger.info("=" * 50)
        logger.info("SCRIPT REPARSING COMPLETED")
        logger.info("=" * 50)
        
        return 0
        
    except Exception as e:
        logger.error(f"Reparsing failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
