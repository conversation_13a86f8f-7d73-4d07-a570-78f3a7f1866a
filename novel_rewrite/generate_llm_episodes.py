#!/usr/bin/env python3
"""
使用真实LLM生成高质量剧集脚本
"""

import json
import sys
import os
import argparse
from pathlib import Path
import logging
from typing import Dict, List, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_paths():
    """设置路径"""
    # 添加主项目路径到sys.path
    main_project_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    if main_project_path not in sys.path:
        sys.path.insert(0, main_project_path)

def extract_spine_events_with_llm(chapters: List[Dict], max_events_per_chapter: int = 4) -> Dict[str, Any]:
    """使用LLM提取Spine事件"""
    setup_paths()
    from modules.langchain_interface import call_llm_json_response
    
    spine_results = {}
    total_events = 0
    
    for chapter in chapters:
        chapter_num = chapter.get('basic_info', {}).get('chapter_number')
        if chapter_num is None:
            continue
            
        logger.info(f"Processing chapter {chapter_num} with LLM...")
        
        # 构建章节内容
        content = chapter.get('narrative', {}).get('content', '')
        key_events = chapter.get('key_events', [])
        characters = chapter.get('key_characters', [])
        
        # 组合内容
        full_content = f"""
章节 {chapter_num}: {chapter.get('basic_info', {}).get('title', '')}

故事内容：
{content}

关键事件：
{json.dumps(key_events, ensure_ascii=False, indent=2)}

主要角色：
{json.dumps(characters, ensure_ascii=False, indent=2)}
"""
        
        try:
            # 调用LLM提取Spine事件
            response = call_llm_json_response(
                api_function="extract_spine_events",
                prompt_data={"content": full_content},
                expected_fields=["spine_events"],
                using_cache=True
            )
            
            if response and "spine_events" in response:
                spine_events = response["spine_events"]
                # 限制事件数量
                if len(spine_events) > max_events_per_chapter:
                    spine_events = spine_events[:max_events_per_chapter]
                
                spine_results[str(chapter_num)] = {
                    "chapter_number": chapter_num,
                    "spine_events": spine_events,
                    "event_count": len(spine_events)
                }
                total_events += len(spine_events)
                logger.info(f"Chapter {chapter_num}: extracted {len(spine_events)} spine events")
            else:
                logger.warning(f"Chapter {chapter_num}: no spine events extracted")
                spine_results[str(chapter_num)] = {
                    "chapter_number": chapter_num,
                    "spine_events": [],
                    "event_count": 0
                }
                
        except Exception as e:
            logger.error(f"Failed to extract spine events for chapter {chapter_num}: {e}")
            spine_results[str(chapter_num)] = {
                "chapter_number": chapter_num,
                "spine_events": [],
                "event_count": 0
            }
    
    logger.info(f"Total spine events extracted: {total_events}")
    return spine_results

def generate_episode_structure_with_llm(episode_info: Dict[str, Any]) -> Dict[str, Any]:
    """使用LLM生成剧集结构"""
    setup_paths()
    from modules.langchain_interface import call_llm_json_response
    
    spine_events = episode_info["spine_events"]
    episode_num = episode_info["episode_number"]
    chapters = episode_info.get("chapters", [])
    
    logger.info(f"Generating structure for episode {episode_num} with LLM...")
    
    prompt_data = {
        "episode_number": episode_num,
        "spine_events": [event.get('text', '') for event in spine_events],
        "chapters": chapters,
        "target_scenes": 5
    }
    
    try:
        response = call_llm_json_response(
            api_function="generate_episode_structure",
            prompt_data=prompt_data,
            expected_fields=["episode_structure"],
            using_cache=True
        )
        
        if response and "episode_structure" in response:
            logger.info(f"Episode {episode_num} structure generated successfully")
            return response
        else:
            logger.error(f"Failed to generate structure for episode {episode_num}")
            return None
            
    except Exception as e:
        logger.error(f"Error generating structure for episode {episode_num}: {e}")
        return None

def generate_episode_script_with_llm(episode_structure: Dict[str, Any]) -> str:
    """使用LLM生成剧集剧本"""
    setup_paths()
    from modules.langchain_interface import call_llm_json_response
    
    episode_num = episode_structure.get("episode_number", 1)
    logger.info(f"Generating script for episode {episode_num} with LLM...")
    
    prompt_data = {
        "episode_number": episode_num,
        "episode_structure": episode_structure,
        "scenes": episode_structure.get("acts", {}),
        "main_plots": episode_structure.get("main_plots", []),
        "characters": episode_structure.get("characters", [])
    }
    
    try:
        response = call_llm_json_response(
            api_function="generate_full_script",
            prompt_data=prompt_data,
            expected_fields=["script"],
            using_cache=True
        )
        
        if response and "script" in response:
            logger.info(f"Episode {episode_num} script generated successfully")
            return response["script"]
        else:
            logger.error(f"Failed to generate script for episode {episode_num}")
            return ""
            
    except Exception as e:
        logger.error(f"Error generating script for episode {episode_num}: {e}")
        return ""

def build_episode_allocation(spine_results: Dict, max_episodes: int = 2) -> List[Dict]:
    """构建剧集分配"""
    chapters = list(spine_results.keys())
    chapters_per_episode = len(chapters) // max_episodes
    
    episodes = []
    for i in range(0, len(chapters), chapters_per_episode):
        episode_chapters = chapters[i:i + chapters_per_episode]
        
        # 收集该集的spine事件
        episode_spine_events = []
        for chapter_id in episode_chapters:
            chapter_data = spine_results[chapter_id]
            episode_spine_events.extend(chapter_data["spine_events"])
        
        episodes.append({
            "episode_number": len(episodes) + 1,
            "chapters": [int(ch) for ch in episode_chapters],
            "spine_events": episode_spine_events
        })
    
    return episodes

def convert_to_animation_format(script_data: Dict, episode_num: int) -> Dict:
    """转换为动画制作格式"""
    
    # 提取角色信息
    characters = []
    if isinstance(script_data, dict) and "characters" in script_data:
        for char in script_data["characters"]:
            characters.append({
                "name": char.get("name", "未知角色"),
                "gender": "male" if any(name in char.get("name", "") for name in ["罗兰", "卡特", "巴罗夫"]) else "female",
                "age": "Adult",
                "role": ["主要角色"],
                "aliases": []
            })
    
    # 提取场景和对话
    scenes = []
    if isinstance(script_data, dict) and "scenes" in script_data:
        for i, scene in enumerate(script_data["scenes"]):
            dialogue = []
            
            # 添加环境音
            if "sound_effects" in scene:
                for effect in scene["sound_effects"]:
                    dialogue.append({
                        "c": "【环境音",
                        "m": "Neutral",
                        "t": effect
                    })
            
            # 添加对话
            if "dialogue" in scene:
                for line in scene["dialogue"]:
                    dialogue.append({
                        "c": line.get("character", "未知"),
                        "m": line.get("mood", "Neutral"),
                        "t": line.get("text", "")
                    })
            
            scenes.append({
                "scene_number": i + 1,
                "n": i + 1,
                "sn": i + 1,
                "shot_type": "dialogue",
                "environment": {
                    "image": scene.get("location", "未指定场景")
                },
                "narration": {
                    "nr": scene.get("narration", "")
                },
                "dialogue": dialogue,
                "sound_cues": []
            })
    
    # 构建完整结构
    return {
        "ep": {
            "ep_n": episode_num,
            "t": f"第{episode_num}集",
            "c": characters,
            "scenes": scenes
        },
        "episode_structure": {
            "episode_number": episode_num,
            "main_plots": script_data.get("main_plots", []) if isinstance(script_data, dict) else [],
            "main_conflict": "核心冲突",
            "characters": [char["name"] for char in characters],
            "acts": {
                "opening": {"purpose": "开场", "scenes": 1},
                "development": {"purpose": "发展", "scenes": 1}, 
                "ending": {"purpose": "结尾", "scenes": 1}
            }
        }
    }

def main():
    parser = argparse.ArgumentParser(description="Generate episodes using real LLM")
    parser.add_argument("input_file", help="Input chapter summaries JSON file")
    parser.add_argument("--output_dir", default="./output_llm_episodes", help="Output directory")
    parser.add_argument("--max_episodes", type=int, default=2, help="Maximum episodes to generate")
    parser.add_argument("--max_events", type=int, default=4, help="Max spine events per chapter")
    
    args = parser.parse_args()
    
    try:
        # 创建输出目录
        output_dir = Path(args.output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # 读取章节数据
        logger.info(f"Loading chapters from {args.input_file}")
        with open(args.input_file, 'r', encoding='utf-8') as f:
            chapters = json.load(f)
        
        logger.info(f"Loaded {len(chapters)} chapters")
        
        # 步骤1: 提取Spine事件
        logger.info("Step 1: Extracting spine events with LLM...")
        spine_results = extract_spine_events_with_llm(chapters, args.max_events)
        
        # 保存spine事件
        spine_file = output_dir / "spine_events.json"
        with open(spine_file, 'w', encoding='utf-8') as f:
            json.dump(spine_results, f, ensure_ascii=False, indent=2)
        
        # 步骤2: 构建剧集分配
        logger.info("Step 2: Building episode allocation...")
        episode_allocation = build_episode_allocation(spine_results, args.max_episodes)
        
        # 步骤3: 生成剧集
        logger.info("Step 3: Generating episodes with LLM...")
        episodes = []
        
        for episode_info in episode_allocation:
            episode_num = episode_info["episode_number"]
            logger.info(f"Generating episode {episode_num}...")
            
            # 生成剧集结构
            structure_response = generate_episode_structure_with_llm(episode_info)
            if not structure_response:
                logger.error(f"Failed to generate structure for episode {episode_num}")
                continue
            
            episode_structure = structure_response.get("episode_structure", {})
            
            # 生成剧本
            episode_script = generate_episode_script_with_llm(episode_structure)
            if not episode_script:
                logger.error(f"Failed to generate script for episode {episode_num}")
                continue
            
            # 转换为动画格式
            animation_script = convert_to_animation_format(episode_script, episode_num)
            
            # 保存剧集
            episode_file = output_dir / f"episode_{episode_num:02d}.json"
            with open(episode_file, 'w', encoding='utf-8') as f:
                json.dump(animation_script, f, ensure_ascii=False, indent=2)
            
            episodes.append(animation_script)
            logger.info(f"Episode {episode_num} completed and saved")
        
        logger.info("=" * 50)
        logger.info("LLM EPISODE GENERATION COMPLETED")
        logger.info("=" * 50)
        logger.info(f"Generated {len(episodes)} episodes")
        logger.info(f"Output directory: {output_dir}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Generation failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
