#!/usr/bin/env python3
"""
冒烟测试 - 验证系统基本功能
"""

import json
import tempfile
import os
import sys
from pathlib import Path

def create_test_data():
    """创建测试数据"""
    test_data = {
        "novel_info": {
            "title": "测试小说",
            "author": "测试作者"
        },
        "chapters": [
            {
                "basic_info": {
                    "chapter_number": 1,
                    "title": "开始",
                    "word_count": 2000
                },
                "narrative": {
                    "content": "罗兰穿越到异世界，成为边境镇的四王子。他发现这个世界有女巫存在，决定保护她们。"
                },
                "key_events": [
                    {"event": "罗兰穿越", "importance": 0.9},
                    {"event": "发现女巫", "importance": 0.8}
                ],
                "key_characters": ["罗兰", "安娜"]
            },
            {
                "basic_info": {
                    "chapter_number": 2,
                    "title": "女巫",
                    "word_count": 2500
                },
                "narrative": {
                    "content": "罗兰遇到女巫安娜，她拥有控制火焰的能力。罗兰决定保护她，而不是处死她。"
                },
                "key_events": [
                    {"event": "遇到安娜", "importance": 0.9},
                    {"event": "决定保护女巫", "importance": 0.95}
                ],
                "key_characters": ["罗兰", "安娜", "卡特"]
            }
        ]
    }
    return test_data

def test_spine_extraction():
    """测试Spine事件抽取"""
    print("Testing Spine extraction...")
    
    try:
        from spine import SpineExtractor
        from alias import AliasManager
        
        # 创建测试数据
        test_data = create_test_data()
        
        # 模拟LLM客户端
        class MockLLMClient:
            def call_json_response(self, prompt, expected_fields=None):
                return {
                    "spine_events": [
                        {
                            "id": "1-1",
                            "type": "plot",
                            "text": "罗兰穿越到异世界",
                            "importance": 0.9,
                            "tension": 0.6
                        }
                    ]
                }
        
        # 测试
        alias_manager = AliasManager()
        llm_client = MockLLMClient()
        extractor = SpineExtractor(llm_client, alias_manager.alias_map)
        
        for chapter in test_data["chapters"]:
            spine_events = extractor.extract_spine_events(chapter)
            assert len(spine_events) > 0, "Should extract at least one spine event"
            assert spine_events[0].importance > 0, "Importance should be positive"
        
        print("✓ Spine extraction test passed")
        return True
        
    except Exception as e:
        print(f"✗ Spine extraction test failed: {e}")
        return False

def test_phase_allocation():
    """测试Phase分配"""
    print("Testing Phase allocation...")
    
    try:
        from phase import PhaseAllocator
        
        # 创建测试组摘要
        group_summaries = {
            "g_001": {
                "group_summary": "开始的故事",
                "main_events": ["穿越"],
                "tension_score": 0.3,
                "token_count": 1000
            },
            "g_002": {
                "group_summary": "激烈的冲突",
                "main_events": ["战斗"],
                "tension_score": 0.8,
                "token_count": 1500
            }
        }
        
        # 测试
        allocator = PhaseAllocator()
        result = allocator.allocate_phases_dynamic(group_summaries)
        
        allocation = result["allocation"]
        assert len(allocation.setup) > 0, "Setup phase should not be empty"
        assert len(allocation.development) >= 0, "Development phase should exist"
        
        print("✓ Phase allocation test passed")
        return True
        
    except Exception as e:
        print(f"✗ Phase allocation test failed: {e}")
        return False

def test_drift_detection():
    """测试漂移检测"""
    print("Testing Drift detection...")
    
    try:
        from drift import DriftDetector
        
        # 测试数据
        script_text = """
        罗兰：我要保护这些女巫。
        安娜：谢谢你，殿下。
        旁白：罗兰做出了重要决定。
        """
        
        spine_events = ["罗兰决定保护女巫", "安娜表示感谢"]
        
        # 测试
        detector = DriftDetector()
        
        # 测试事件抽取
        script_events = detector.extract_script_events(script_text)
        assert len(script_events) > 0, "Should extract script events"
        
        # 测试漂移检测
        quality_report = detector.detect_drift(script_text, spine_events, 1)
        assert quality_report.episode_num == 1, "Episode number should match"
        assert 0 <= quality_report.divergence_score <= 1, "Divergence score should be in [0,1]"
        
        print("✓ Drift detection test passed")
        return True
        
    except Exception as e:
        print(f"✗ Drift detection test failed: {e}")
        return False

def test_end_state_management():
    """测试结束状态管理"""
    print("Testing End state management...")
    
    try:
        from end_state import EndStateManager
        
        # 测试数据
        script_text = """
        罗兰：我的目标是保护边境镇。
        安娜：我会帮助你的。
        旁白：他们面临着巨大的危险。
        """
        
        # 测试
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = EndStateManager(temp_dir)
            
            end_state = manager.generate_end_state(script_text, 1)
            assert len(end_state.char_states) > 0, "Should have character states"
            assert end_state.checksum is not None, "Should have checksum"
            
            # 测试保存和加载
            loaded_state = manager.load_end_state(1)
            assert loaded_state is not None, "Should load saved state"
            assert loaded_state.checksum == end_state.checksum, "Checksums should match"
        
        print("✓ End state management test passed")
        return True
        
    except Exception as e:
        print(f"✗ End state management test failed: {e}")
        return False

def test_io_operations():
    """测试I/O操作"""
    print("Testing I/O operations...")
    
    try:
        from load_save import save_json, load_json, save_text, load_text
        
        test_data = {"test": "data", "number": 42}
        test_text = "This is a test text."
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 测试JSON操作
            json_file = os.path.join(temp_dir, "test.json")
            save_json(test_data, json_file)
            loaded_data = load_json(json_file)
            assert loaded_data == test_data, "JSON data should match"
            
            # 测试文本操作
            text_file = os.path.join(temp_dir, "test.txt")
            save_text(test_text, text_file)
            loaded_text = load_text(text_file)
            assert loaded_text == test_text, "Text data should match"
        
        print("✓ I/O operations test passed")
        return True
        
    except Exception as e:
        print(f"✗ I/O operations test failed: {e}")
        return False

def test_vector_cache():
    """测试向量缓存"""
    print("Testing Vector cache...")
    
    try:
        from vector_cache import VectorCache
        import numpy as np
        
        with tempfile.TemporaryDirectory() as temp_dir:
            cache = VectorCache(temp_dir, memory_limit=10)
            
            # 测试缓存操作
            test_vector = np.random.rand(768)
            cache.set_vector("test_text", "test_model", test_vector)
            
            # 测试获取
            retrieved_vector = cache.get_vector("test_text", "test_model")
            assert retrieved_vector is not None, "Should retrieve cached vector"
            assert np.allclose(test_vector, retrieved_vector), "Vectors should match"
            
            # 测试统计
            stats = cache.get_cache_stats()
            assert stats["total_requests"] > 0, "Should have request stats"
        
        print("✓ Vector cache test passed")
        return True
        
    except Exception as e:
        print(f"✗ Vector cache test failed: {e}")
        return False

def test_full_pipeline():
    """测试完整流水线"""
    print("Testing Full pipeline...")
    
    try:
        import subprocess
        
        # 创建测试输入文件
        test_data = create_test_data()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            input_file = os.path.join(temp_dir, "test_input.json")
            with open(input_file, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False, indent=2)
            
            # 运行spine构建
            script_dir = Path(__file__).parent
            spine_cmd = [
                sys.executable, str(script_dir / "build_spine.py"),
                input_file,
                "--output", os.path.join(temp_dir, "spine_events.json")
            ]
            
            result = subprocess.run(spine_cmd, capture_output=True, text=True)
            assert result.returncode == 0, f"Spine building failed: {result.stderr}"
            
            # 检查输出文件
            spine_file = os.path.join(temp_dir, "spine_events.json")
            assert os.path.exists(spine_file), "Spine events file should exist"
            
            with open(spine_file, 'r', encoding='utf-8') as f:
                spine_data = json.load(f)
            assert len(spine_data) > 0, "Should have spine events"
        
        print("✓ Full pipeline test passed")
        return True
        
    except Exception as e:
        print(f"✗ Full pipeline test failed: {e}")
        return False

def main():
    """运行所有测试"""
    print("Running smoke tests for novel_rewrite system...")
    print("=" * 50)
    
    tests = [
        test_io_operations,
        test_spine_extraction,
        test_phase_allocation,
        test_drift_detection,
        test_end_state_management,
        test_vector_cache,
        test_full_pipeline
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! System is ready to use.")
        return 0
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
