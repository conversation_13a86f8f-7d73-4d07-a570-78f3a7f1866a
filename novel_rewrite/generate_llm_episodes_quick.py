#!/usr/bin/env python3
"""
快速版本：使用真实LLM生成前2集剧本（仅处理前8章）
"""

import json
import sys
import os
import argparse
from pathlib import Path
import logging
from typing import Dict, List, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_paths():
    """设置路径"""
    # 添加主项目路径到sys.path
    main_project_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    if main_project_path not in sys.path:
        sys.path.insert(0, main_project_path)

def extract_spine_events_batch(chapters: List[Dict], max_chapters: int = 8) -> Dict[str, Any]:
    """批量提取前几章的Spine事件"""
    setup_paths()
    from modules.langchain_interface import call_llm_json_response

    # 只处理前max_chapters章
    selected_chapters = chapters[:max_chapters]
    spine_results = {}

    for chapter in selected_chapters:
        chapter_num = chapter.get('basic_info', {}).get('chapter_number')
        if chapter_num is None:
            continue

        logger.info(f"Processing chapter {chapter_num} with LLM...")

        # 构建章节内容
        content = chapter.get('narrative', {}).get('content', '')
        key_events = chapter.get('key_events', [])

        # 简化内容以加快处理
        full_content = f"""
章节 {chapter_num}: {chapter.get('basic_info', {}).get('title', '')}

故事内容：
{content[:1000]}...  # 截取前1000字符

关键事件：
{json.dumps(key_events[:3], ensure_ascii=False)}  # 只取前3个事件
"""

        try:
            # 调用LLM提取Spine事件
            response = call_llm_json_response(
                api_function="extract_spine_events",
                prompt_data={"content": full_content},
                expected_fields=["spine_events"],
                using_cache=True
            )

            if response and "spine_events" in response:
                spine_events = response["spine_events"]
                spine_results[str(chapter_num)] = {
                    "chapter_number": chapter_num,
                    "spine_events": spine_events,
                    "event_count": len(spine_events)
                }
                logger.info(f"Chapter {chapter_num}: extracted {len(spine_events)} spine events")
            else:
                logger.warning(f"Chapter {chapter_num}: no spine events extracted")

        except Exception as e:
            logger.error(f"Failed to extract spine events for chapter {chapter_num}: {e}")

    return spine_results

def generate_episode_with_llm(episode_info: Dict[str, Any]) -> Dict[str, Any]:
    """使用LLM生成完整剧集"""
    setup_paths()
    from modules.langchain_interface import call_llm_json_response

    episode_num = episode_info["episode_number"]
    spine_events = episode_info["spine_events"]
    chapters = episode_info.get("chapters", [])

    logger.info(f"Generating complete episode {episode_num} with LLM...")

    # 直接生成完整剧本，跳过中间步骤
    spine_events_text = [event.get('text', '') for event in spine_events]
    main_plots = spine_events_text[:3]
    characters = ["罗兰", "安娜", "巴罗夫", "卡特"]

    prompt_data = {
        "episode_number": episode_num,
        "spine_events": spine_events_text,
        "main_plots": main_plots,
        "characters": characters
    }

    try:
        response = call_llm_json_response(
            api_function="generate_full_script",
            prompt_data=prompt_data,
            expected_fields=["script"],
            using_cache=True
        )

        if response and "script" in response:
            logger.info(f"Episode {episode_num} generated successfully")
            return response["script"]
        else:
            logger.error(f"Failed to generate episode {episode_num}")
            return None

    except Exception as e:
        logger.error(f"Error generating episode {episode_num}: {e}")
        return None

def convert_to_animation_format(script_data: Dict, episode_num: int) -> Dict:
    """转换为动画制作格式"""

    # 默认角色信息
    default_characters = [
        {"name": "罗兰", "gender": "male", "age": "Adult", "role": ["主角"], "aliases": []},
        {"name": "安娜", "gender": "female", "age": "Young", "role": ["女巫"], "aliases": []},
        {"name": "巴罗夫", "gender": "male", "age": "Adult", "role": ["助手"], "aliases": []},
        {"name": "卡特", "gender": "male", "age": "Adult", "role": ["骑士"], "aliases": []}
    ]

    # 提取角色信息
    characters = default_characters
    if isinstance(script_data, dict) and "characters" in script_data:
        characters = []
        for char in script_data["characters"]:
            characters.append({
                "name": char.get("name", "未知角色"),
                "gender": char.get("voice_type", "male"),
                "age": "Adult",
                "role": [char.get("personality", "角色")],
                "aliases": []
            })

    # 提取场景和对话
    scenes = []
    if isinstance(script_data, dict) and "scenes" in script_data:
        for i, scene in enumerate(script_data["scenes"]):
            dialogue = []

            # 添加场景描述
            if "atmosphere" in scene:
                dialogue.append({
                    "c": "【旁白",
                    "m": "Neutral",
                    "t": scene["atmosphere"]
                })

            # 添加环境音
            if "sound_effects" in scene:
                for effect in scene["sound_effects"]:
                    dialogue.append({
                        "c": "【环境音",
                        "m": "Neutral",
                        "t": effect
                    })

            # 添加对话
            if "dialogue" in scene:
                for line in scene["dialogue"]:
                    dialogue.append({
                        "c": line.get("character", "未知"),
                        "m": line.get("mood", "Neutral"),
                        "t": line.get("text", "")
                    })

            # 添加旁白
            if "narration" in scene and scene["narration"]:
                dialogue.append({
                    "c": "【旁白",
                    "m": "Neutral",
                    "t": scene["narration"]
                })

            scenes.append({
                "scene_number": i + 1,
                "n": i + 1,
                "sn": i + 1,
                "shot_type": "dialogue",
                "environment": {
                    "image": scene.get("location", "边陲镇")
                },
                "narration": {
                    "nr": scene.get("narration", "")
                },
                "dialogue": dialogue,
                "sound_cues": []
            })

    # 如果没有场景，创建默认场景
    if not scenes:
        default_dialogue = [
            {"c": "【旁白", "m": "Neutral", "t": f"第{episode_num}集开始..."},
            {"c": "罗兰", "m": "Thoughtful", "t": "这个世界...到底是怎么回事？"},
            {"c": "【环境音", "m": "Neutral", "t": "风声呼啸，远处传来人群的声音。"}
        ]

        scenes = [{
            "scene_number": 1,
            "n": 1,
            "sn": 1,
            "shot_type": "dialogue",
            "environment": {"image": "边陲镇"},
            "narration": {"nr": f"第{episode_num}集的故事开始"},
            "dialogue": default_dialogue,
            "sound_cues": []
        }]

    # 构建完整结构
    return {
        "ep": {
            "ep_n": episode_num,
            "t": f"第{episode_num}集",
            "c": characters,
            "scenes": scenes
        },
        "episode_structure": {
            "episode_number": episode_num,
            "main_plots": script_data.get("main_plots", []) if isinstance(script_data, dict) else [],
            "main_conflict": "核心冲突",
            "characters": [char["name"] for char in characters],
            "acts": {
                "opening": {"purpose": "开场", "scenes": 1},
                "development": {"purpose": "发展", "scenes": 1},
                "ending": {"purpose": "结尾", "scenes": 1}
            }
        }
    }

def main():
    parser = argparse.ArgumentParser(description="Quick LLM episode generation (first 8 chapters)")
    parser.add_argument("input_file", help="Input chapter summaries JSON file")
    parser.add_argument("--output_dir", default="./output_llm_quick", help="Output directory")
    parser.add_argument("--max_chapters", type=int, default=8, help="Max chapters to process")

    args = parser.parse_args()

    try:
        # 创建输出目录
        output_dir = Path(args.output_dir)
        output_dir.mkdir(exist_ok=True)

        # 读取章节数据
        logger.info(f"Loading chapters from {args.input_file}")
        with open(args.input_file, 'r', encoding='utf-8') as f:
            chapters = json.load(f)

        logger.info(f"Processing first {args.max_chapters} chapters out of {len(chapters)} total")

        # 步骤1: 提取Spine事件
        logger.info("Step 1: Extracting spine events with LLM...")
        spine_results = extract_spine_events_batch(chapters, args.max_chapters)

        # 保存spine事件
        spine_file = output_dir / "spine_events.json"
        with open(spine_file, 'w', encoding='utf-8') as f:
            json.dump(spine_results, f, ensure_ascii=False, indent=2)

        # 步骤2: 构建剧集分配（前4章一集）
        logger.info("Step 2: Building episode allocation...")
        chapters_per_episode = args.max_chapters // 2

        episodes = []
        for ep_num in range(1, 3):  # 生成2集
            start_chapter = (ep_num - 1) * chapters_per_episode + 1
            end_chapter = ep_num * chapters_per_episode

            # 收集该集的spine事件
            episode_spine_events = []
            episode_chapters = []

            for ch_num in range(start_chapter, end_chapter + 1):
                if str(ch_num) in spine_results:
                    episode_spine_events.extend(spine_results[str(ch_num)]["spine_events"])
                    episode_chapters.append(ch_num)

            episode_info = {
                "episode_number": ep_num,
                "chapters": episode_chapters,
                "spine_events": episode_spine_events
            }

            # 步骤3: 生成剧集
            logger.info(f"Step 3: Generating episode {ep_num} with LLM...")
            episode_script = generate_episode_with_llm(episode_info)

            if episode_script:
                # 转换为动画格式
                animation_script = convert_to_animation_format(episode_script, ep_num)

                # 保存剧集
                episode_file = output_dir / f"episode_{ep_num:02d}.json"
                with open(episode_file, 'w', encoding='utf-8') as f:
                    json.dump(animation_script, f, ensure_ascii=False, indent=2)

                episodes.append(animation_script)
                logger.info(f"Episode {ep_num} completed and saved")
            else:
                logger.error(f"Failed to generate episode {ep_num}")

        logger.info("=" * 50)
        logger.info("QUICK LLM EPISODE GENERATION COMPLETED")
        logger.info("=" * 50)
        logger.info(f"Generated {len(episodes)} episodes from {args.max_chapters} chapters")
        logger.info(f"Output directory: {output_dir}")

        return 0

    except Exception as e:
        logger.error(f"Generation failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
