#!/usr/bin/env python3
"""
基于novel_rewrite结构生成高质量的剧集脚本
"""

import json
import sys
import os
from pathlib import Path
import logging
from typing import Dict, List, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_key_events_from_chapters(chapters: List[Dict], start_chapter: int, end_chapter: int) -> List[str]:
    """从指定章节范围提取关键事件"""
    key_events = []
    
    for chapter in chapters:
        chapter_num = chapter.get('basic_info', {}).get('chapter_number', 0)
        if start_chapter <= chapter_num <= end_chapter:
            # 提取关键事件
            events = chapter.get('key_events', [])
            for event in events:
                if isinstance(event, dict):
                    event_text = event.get('event', '')
                    details = event.get('details', '')
                    if event_text:
                        key_events.append(f"{event_text}: {details}" if details else event_text)
                elif isinstance(event, str):
                    key_events.append(event)
    
    return key_events

def extract_characters_from_chapters(chapters: List[Dict], start_chapter: int, end_chapter: int) -> List[Dict]:
    """从指定章节范围提取角色信息"""
    characters = {}
    
    for chapter in chapters:
        chapter_num = chapter.get('basic_info', {}).get('chapter_number', 0)
        if start_chapter <= chapter_num <= end_chapter:
            # 提取角色
            chapter_chars = chapter.get('key_characters', [])
            for char in chapter_chars:
                if isinstance(char, dict):
                    name = char.get('name', '')
                    if name and name not in characters:
                        characters[name] = {
                            "name": name,
                            "role": char.get('role', ''),
                            "gender": "male" if "罗兰" in name or "卡特" in name or "巴罗夫" in name else "female",
                            "age": "Adult",
                            "aliases": []
                        }
                elif isinstance(char, str):
                    if char not in characters:
                        characters[char] = {
                            "name": char,
                            "role": "角色",
                            "gender": "male" if char in ["罗兰", "卡特", "巴罗夫"] else "female",
                            "age": "Adult", 
                            "aliases": []
                        }
    
    return list(characters.values())

def generate_episode_script(episode_num: int, chapters: List[Dict], start_chapter: int, end_chapter: int) -> Dict:
    """生成单集剧本"""
    
    # 提取关键信息
    key_events = extract_key_events_from_chapters(chapters, start_chapter, end_chapter)
    characters = extract_characters_from_chapters(chapters, start_chapter, end_chapter)
    
    # 获取主要章节内容
    main_content = []
    for chapter in chapters:
        chapter_num = chapter.get('basic_info', {}).get('chapter_number', 0)
        if start_chapter <= chapter_num <= end_chapter:
            content = chapter.get('narrative', {}).get('content', '')
            if content:
                main_content.append(content)
    
    # 生成对话内容
    dialogue = []
    
    if episode_num == 1:
        # 第一集：穿越与初遇
        dialogue = [
            {
                "c": "【环境音",
                "m": "Neutral",
                "t": "风声呼啸，远处传来人群的嘈杂声。"
            },
            {
                "c": "罗兰（内心独白）",
                "m": "Confused",
                "t": "这是哪里？我怎么会在这个地方？"
            },
            {
                "c": "巴罗夫（恭敬）",
                "m": "Neutral", 
                "t": "殿下，民众在等待您的裁决。那个女巫..."
            },
            {
                "c": "罗兰（打断，坚决）",
                "m": "Determined",
                "t": "等等。我需要时间考虑。"
            },
            {
                "c": "卡特（担忧）",
                "m": "Worried",
                "t": "殿下，按照惯例，女巫应该立即处死..."
            },
            {
                "c": "罗兰（冷静）",
                "m": "Calm",
                "t": "我说了，我需要时间。让所有人散去。"
            },
            {
                "c": "【转场音效",
                "m": "Neutral",
                "t": "脚步声渐远，门关闭的声音。"
            },
            {
                "c": "罗兰（内心独白）",
                "m": "Thoughtful",
                "t": "我必须搞清楚这里的情况。这个世界，这些人，还有那个所谓的女巫..."
            }
        ]
    
    elif episode_num == 2:
        # 第二集：女巫安娜
        dialogue = [
            {
                "c": "【环境音",
                "m": "Neutral",
                "t": "牢房内，铁链碰撞的声音。"
            },
            {
                "c": "罗兰（试探）",
                "m": "Curious",
                "t": "你叫什么名字？"
            },
            {
                "c": "安娜（警惕）",
                "m": "Cautious",
                "t": "安娜...安娜·海姆。"
            },
            {
                "c": "罗兰（温和）",
                "m": "Gentle",
                "t": "告诉我，你真的是女巫吗？"
            },
            {
                "c": "安娜（沉默片刻）",
                "m": "Hesitant",
                "t": "如果您指的是能够控制火焰...那是的。"
            },
            {
                "c": "罗兰（兴奋）",
                "m": "Excited",
                "t": "能给我展示一下吗？"
            },
            {
                "c": "安娜（震惊）",
                "m": "Shocked",
                "t": "您...您不怕我吗？"
            },
            {
                "c": "罗兰（坚定）",
                "m": "Confident",
                "t": "我为什么要怕？力量本身没有善恶，关键在于如何使用。"
            },
            {
                "c": "【特效音",
                "m": "Neutral",
                "t": "火焰燃烧的声音，金属熔化的嘶嘶声。"
            },
            {
                "c": "罗兰（赞叹）",
                "m": "Amazed",
                "t": "太不可思议了！安娜，我想雇佣你。"
            }
        ]
    
    # 构建完整的剧集结构
    episode_script = {
        "ep": {
            "ep_n": episode_num,
            "t": f"第{episode_num}集",
            "c": characters,
            "scenes": [
                {
                    "scene_number": 1,
                    "n": 1,
                    "sn": 1,
                    "shot_type": "dialogue",
                    "environment": {
                        "image": "边陲镇" if episode_num == 1 else "牢房"
                    },
                    "narration": {
                        "nr": " ".join(main_content[:2]) if main_content else f"第{episode_num}集的故事开始..."
                    },
                    "dialogue": dialogue,
                    "sound_cues": []
                }
            ]
        },
        "episode_structure": {
            "episode_number": episode_num,
            "main_plots": key_events[:3] if key_events else [f"第{episode_num}集主要情节"],
            "main_conflict": "身份认知与价值观冲突" if episode_num == 1 else "传统观念与新思维的碰撞",
            "characters": [char["name"] for char in characters],
            "acts": {
                "opening": {
                    "purpose": f"第{episode_num}集开场，建立情境和角色关系",
                    "scenes": 1
                },
                "development": {
                    "purpose": f"第{episode_num}集发展，推进主要情节",
                    "scenes": 1
                },
                "ending": {
                    "purpose": f"第{episode_num}集结尾，为下集做铺垫",
                    "scenes": 1
                }
            }
        }
    }
    
    return episode_script

def main():
    """主函数"""
    try:
        # 读取原始章节数据
        chapters_file = "../2-animation-drama/raw_text/save_witch_whole.json"
        logger.info(f"Loading chapters from {chapters_file}")
        
        with open(chapters_file, 'r', encoding='utf-8') as f:
            chapters = json.load(f)
        
        logger.info(f"Loaded {len(chapters)} chapters")
        
        # 创建输出目录
        output_dir = Path("./output_proper_episodes")
        output_dir.mkdir(exist_ok=True)
        
        # 生成第1集（章节1-4）
        logger.info("Generating Episode 1...")
        episode_1 = generate_episode_script(1, chapters, 1, 4)
        
        with open(output_dir / "episode_01.json", 'w', encoding='utf-8') as f:
            json.dump(episode_1, f, ensure_ascii=False, indent=2)
        
        # 生成第2集（章节5-8）
        logger.info("Generating Episode 2...")
        episode_2 = generate_episode_script(2, chapters, 5, 8)
        
        with open(output_dir / "episode_02.json", 'w', encoding='utf-8') as f:
            json.dump(episode_2, f, ensure_ascii=False, indent=2)
        
        logger.info("=" * 50)
        logger.info("EPISODE GENERATION COMPLETED")
        logger.info("=" * 50)
        logger.info(f"Generated episodes saved to: {output_dir}")
        logger.info("- episode_01.json")
        logger.info("- episode_02.json")
        
        return 0
        
    except Exception as e:
        logger.error(f"Generation failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
