"""
向量缓存管理系统
"""

import sqlite3
import numpy as np
import hashlib
import pickle
import logging
from typing import Optional, Dict, Any, List
from pathlib import Path
from collections import OrderedDict
import threading

logger = logging.getLogger(__name__)

class VectorCache:
    """三层向量缓存系统"""
    
    def __init__(self, cache_dir: str = "./cache", memory_limit: int = 1000):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # L1: 内存LRU缓存
        self.memory_cache = OrderedDict()
        self.memory_limit = memory_limit
        self.cache_lock = threading.Lock()
        
        # L2: SQLite持久化
        self.db_path = self.cache_dir / "vector_cache.db"
        self._init_database()
        
        # 统计信息
        self.stats = {
            "memory_hits": 0,
            "db_hits": 0,
            "misses": 0,
            "total_requests": 0
        }
    
    def _init_database(self) -> None:
        """初始化SQLite数据库"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS vector_cache (
                        event_id TEXT,
                        model_name TEXT,
                        vector_blob BLOB,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        access_count INTEGER DEFAULT 1,
                        PRIMARY KEY (event_id, model_name)
                    )
                """)
                
                # 创建索引
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_access_count 
                    ON vector_cache(access_count)
                """)
                
                conn.commit()
                logger.info("Vector cache database initialized")
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def _get_cache_key(self, text: str, model_name: str) -> str:
        """生成缓存键"""
        content = f"{text}:{model_name}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def get_vector(self, text: str, model_name: str) -> Optional[np.ndarray]:
        """获取向量（三层缓存查找）"""
        cache_key = self._get_cache_key(text, model_name)
        self.stats["total_requests"] += 1
        
        # L1: 内存缓存
        with self.cache_lock:
            if cache_key in self.memory_cache:
                # 移到最后（LRU更新）
                vector = self.memory_cache.pop(cache_key)
                self.memory_cache[cache_key] = vector
                self.stats["memory_hits"] += 1
                return vector
        
        # L2: SQLite数据库
        vector = self._get_from_database(cache_key, model_name)
        if vector is not None:
            # 加入内存缓存
            self._add_to_memory_cache(cache_key, vector)
            self.stats["db_hits"] += 1
            return vector
        
        # L3: 缓存未命中
        self.stats["misses"] += 1
        return None
    
    def set_vector(self, text: str, model_name: str, vector: np.ndarray) -> None:
        """设置向量（写入所有层级）"""
        cache_key = self._get_cache_key(text, model_name)
        
        # 写入内存缓存
        self._add_to_memory_cache(cache_key, vector)
        
        # 异步写入数据库
        self._save_to_database(cache_key, model_name, vector)
    
    def _add_to_memory_cache(self, cache_key: str, vector: np.ndarray) -> None:
        """添加到内存缓存"""
        with self.cache_lock:
            # 如果超出限制，删除最旧的
            while len(self.memory_cache) >= self.memory_limit:
                self.memory_cache.popitem(last=False)
            
            self.memory_cache[cache_key] = vector.copy()
    
    def _get_from_database(self, cache_key: str, model_name: str) -> Optional[np.ndarray]:
        """从数据库获取向量"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.execute("""
                    SELECT vector_blob FROM vector_cache 
                    WHERE event_id = ? AND model_name = ?
                """, (cache_key, model_name))
                
                row = cursor.fetchone()
                if row:
                    # 更新访问计数
                    conn.execute("""
                        UPDATE vector_cache 
                        SET access_count = access_count + 1 
                        WHERE event_id = ? AND model_name = ?
                    """, (cache_key, model_name))
                    conn.commit()
                    
                    # 反序列化向量
                    return pickle.loads(row[0])
                
                return None
        except Exception as e:
            logger.error(f"Failed to get vector from database: {e}")
            return None
    
    def _save_to_database(self, cache_key: str, model_name: str, vector: np.ndarray) -> None:
        """保存向量到数据库"""
        try:
            vector_blob = pickle.dumps(vector)
            
            with sqlite3.connect(str(self.db_path)) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO vector_cache 
                    (event_id, model_name, vector_blob) 
                    VALUES (?, ?, ?)
                """, (cache_key, model_name, vector_blob))
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to save vector to database: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        hit_rate = 0.0
        if self.stats["total_requests"] > 0:
            hits = self.stats["memory_hits"] + self.stats["db_hits"]
            hit_rate = hits / self.stats["total_requests"]
        
        # 获取数据库统计
        db_stats = self._get_database_stats()
        
        return {
            "memory_cache_size": len(self.memory_cache),
            "memory_limit": self.memory_limit,
            "hit_rate": hit_rate,
            "memory_hits": self.stats["memory_hits"],
            "db_hits": self.stats["db_hits"],
            "misses": self.stats["misses"],
            "total_requests": self.stats["total_requests"],
            "database_records": db_stats["total_records"],
            "database_size_mb": db_stats["size_mb"]
        }
    
    def _get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                # 记录数量
                cursor = conn.execute("SELECT COUNT(*) FROM vector_cache")
                total_records = cursor.fetchone()[0]
                
                # 数据库文件大小
                size_mb = self.db_path.stat().st_size / (1024 * 1024)
                
                return {
                    "total_records": total_records,
                    "size_mb": round(size_mb, 2)
                }
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            return {"total_records": 0, "size_mb": 0.0}
    
    def cleanup_database(self, max_age_days: int = 30, min_access_count: int = 1) -> int:
        """清理数据库中的旧向量"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.execute("""
                    DELETE FROM vector_cache 
                    WHERE created_at < datetime('now', '-{} days') 
                    AND access_count < ?
                """.format(max_age_days), (min_access_count,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"Cleaned up {deleted_count} old vectors from database")
                return deleted_count
        except Exception as e:
            logger.error(f"Failed to cleanup database: {e}")
            return 0
    
    def clear_memory_cache(self) -> None:
        """清空内存缓存"""
        with self.cache_lock:
            self.memory_cache.clear()
        logger.info("Memory cache cleared")
