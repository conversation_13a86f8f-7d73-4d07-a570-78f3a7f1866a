#!/usr/bin/env python3
"""
比较新旧剧集的差异
"""

import json
import sys
from pathlib import Path

def load_episode(file_path):
    """加载剧集文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def compare_episodes():
    """比较新旧剧集"""
    
    print("=" * 60)
    print("剧集对比分析")
    print("=" * 60)
    
    # 文件路径
    old_ep1 = "../2-animation-drama/episodes/save_witch_whole/episode_01.json"
    new_ep1 = "../2-animation-drama/episodes/save_witch_whole/episode_01_new.json"
    old_ep2 = "../2-animation-drama/episodes/save_witch_whole/episode_02.json"
    new_ep2 = "../2-animation-drama/episodes/save_witch_whole/episode_02_new.json"
    
    # 加载文件
    old_1 = load_episode(old_ep1)
    new_1 = load_episode(new_ep1)
    old_2 = load_episode(old_ep2)
    new_2 = load_episode(new_ep2)
    
    if not all([old_1, new_1, old_2, new_2]):
        print("无法加载所有剧集文件")
        return
    
    print("\n第1集对比:")
    print("-" * 40)
    
    # 角色数量对比
    old_chars_1 = len(old_1.get('ep', {}).get('c', []))
    new_chars_1 = len(new_1.get('ep', {}).get('c', []))
    print(f"角色数量: 旧版 {old_chars_1} vs 新版 {new_chars_1}")
    
    # 对话数量对比
    old_dialogues_1 = 0
    new_dialogues_1 = 0
    
    for scene in old_1.get('ep', {}).get('scenes', []):
        old_dialogues_1 += len(scene.get('dialogue', []))
    
    for scene in new_1.get('ep', {}).get('scenes', []):
        new_dialogues_1 += len(scene.get('dialogue', []))
    
    print(f"对话数量: 旧版 {old_dialogues_1} vs 新版 {new_dialogues_1}")
    
    # 显示新版角色
    print("\n新版第1集角色:")
    for char in new_1.get('ep', {}).get('c', []):
        name = char.get('name', '未知')
        role = char.get('role', '未知')
        print(f"  - {name}: {role}")
    
    # 显示新版对话示例
    print("\n新版第1集对话示例:")
    dialogues = new_1.get('ep', {}).get('scenes', [{}])[0].get('dialogue', [])
    for i, dialogue in enumerate(dialogues[:5]):  # 只显示前5条
        speaker = dialogue.get('c', '未知')
        text = dialogue.get('t', '')
        print(f"  {i+1}. {speaker}: {text}")
    
    print("\n" + "=" * 60)
    print("\n第2集对比:")
    print("-" * 40)
    
    # 角色数量对比
    old_chars_2 = len(old_2.get('ep', {}).get('c', []))
    new_chars_2 = len(new_2.get('ep', {}).get('c', []))
    print(f"角色数量: 旧版 {old_chars_2} vs 新版 {new_chars_2}")
    
    # 对话数量对比
    old_dialogues_2 = 0
    new_dialogues_2 = 0
    
    for scene in old_2.get('ep', {}).get('scenes', []):
        old_dialogues_2 += len(scene.get('dialogue', []))
    
    for scene in new_2.get('ep', {}).get('scenes', []):
        new_dialogues_2 += len(scene.get('dialogue', []))
    
    print(f"对话数量: 旧版 {old_dialogues_2} vs 新版 {new_dialogues_2}")
    
    # 显示新版角色
    print("\n新版第2集角色:")
    for char in new_2.get('ep', {}).get('c', []):
        name = char.get('name', '未知')
        role = char.get('role', '未知')
        print(f"  - {name}: {role}")
    
    # 显示新版对话示例
    print("\n新版第2集对话示例:")
    dialogues = new_2.get('ep', {}).get('scenes', [{}])[0].get('dialogue', [])
    for i, dialogue in enumerate(dialogues[:5]):  # 只显示前5条
        speaker = dialogue.get('c', '未知')
        text = dialogue.get('t', '')
        print(f"  {i+1}. {speaker}: {text}")
    
    print("\n" + "=" * 60)
    print("总结:")
    print("-" * 40)
    print("新版剧集的主要改进:")
    print("1. 角色信息更加规范化和去重")
    print("2. 对话内容更加精炼和有针对性")
    print("3. 场景设计更加聚焦核心情节")
    print("4. 基于Spine事件的故事主线更清晰")
    print("5. 符合动画制作的标准化格式")
    
    print(f"\n文件大小对比:")
    
    # 计算文件大小
    old_1_size = Path(old_ep1).stat().st_size / 1024
    new_1_size = Path(new_ep1).stat().st_size / 1024
    old_2_size = Path(old_ep2).stat().st_size / 1024
    new_2_size = Path(new_ep2).stat().st_size / 1024
    
    print(f"第1集: 旧版 {old_1_size:.1f}KB vs 新版 {new_1_size:.1f}KB")
    print(f"第2集: 旧版 {old_2_size:.1f}KB vs 新版 {new_2_size:.1f}KB")

if __name__ == "__main__":
    compare_episodes()
