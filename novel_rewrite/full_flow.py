#!/usr/bin/env python3
"""
完整流水线 - 一键执行所有步骤
"""

import argparse
import logging
import sys
import os
import subprocess
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(cmd: list, description: str) -> bool:
    """运行命令并处理结果"""
    logger.info(f"Running: {description}")
    logger.debug(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        # 输出标准输出
        if result.stdout:
            for line in result.stdout.strip().split('\n'):
                logger.info(f"  {line}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {description}")
        logger.error(f"Return code: {e.returncode}")
        
        if e.stdout:
            logger.error("STDOUT:")
            for line in e.stdout.strip().split('\n'):
                logger.error(f"  {line}")
        
        if e.stderr:
            logger.error("STDERR:")
            for line in e.stderr.strip().split('\n'):
                logger.error(f"  {line}")
        
        return False

def main():
    parser = argparse.ArgumentParser(description="Complete Novel Rewrite Pipeline")
    parser.add_argument("input_file", help="Input JSON file with chapter summaries")
    parser.add_argument("--output_dir", default="./output", help="Output directory")
    parser.add_argument("--max_episodes", type=int, help="Maximum number of episodes")
    parser.add_argument("--compression", type=float, default=0.3, help="Compression ratio")
    parser.add_argument("--auto_alias", action="store_true", help="Auto-discover aliases")
    parser.add_argument("--max_events", type=int, default=4, help="Max spine events per chapter")
    parser.add_argument("--debug", action="store_true", help="Save debug information")
    parser.add_argument("--skip_spine", action="store_true", help="Skip spine building (use existing)")
    parser.add_argument("--skip_phases", action="store_true", help="Skip phase allocation (use existing)")
    
    args = parser.parse_args()
    
    try:
        # 确保输出目录存在
        output_dir = Path(args.output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # 获取当前脚本目录
        script_dir = Path(__file__).parent
        
        # 定义中间文件路径
        spine_file = output_dir / "spine_events.json"
        phase_file = output_dir / "phase_allocation.json"
        
        logger.info("Starting complete novel rewrite pipeline")
        logger.info(f"Input: {args.input_file}")
        logger.info(f"Output directory: {args.output_dir}")
        
        # 步骤1: 构建Spine事件
        if not args.skip_spine:
            logger.info("=" * 50)
            logger.info("STEP 1: Building Spine Events")
            logger.info("=" * 50)
            
            spine_cmd = [
                sys.executable, str(script_dir / "build_spine.py"),
                args.input_file,
                "--output", str(spine_file),
                "--max_events", str(args.max_events)
            ]
            
            if args.auto_alias:
                spine_cmd.append("--auto_alias")
            
            if not run_command(spine_cmd, "Spine event extraction"):
                logger.error("Spine building failed")
                return 1
        else:
            logger.info("Skipping spine building (using existing file)")
            if not spine_file.exists():
                logger.error(f"Spine file not found: {spine_file}")
                return 1
        
        # 步骤2: 分配Phase
        if not args.skip_phases:
            logger.info("=" * 50)
            logger.info("STEP 2: Allocating Phases")
            logger.info("=" * 50)
            
            phase_cmd = [
                sys.executable, str(script_dir / "build_phases.py"),
                str(spine_file),
                "--output", str(phase_file)
            ]
            
            if args.debug:
                phase_cmd.append("--debug")
            
            if not run_command(phase_cmd, "Phase allocation"):
                logger.error("Phase allocation failed")
                return 1
        else:
            logger.info("Skipping phase allocation (using existing file)")
            if not phase_file.exists():
                logger.error(f"Phase file not found: {phase_file}")
                return 1
        
        # 步骤3: 生成剧集
        logger.info("=" * 50)
        logger.info("STEP 3: Generating Episodes")
        logger.info("=" * 50)
        
        episode_cmd = [
            sys.executable, str(script_dir / "build_episode.py"),
            str(spine_file),
            str(phase_file),
            "--output_dir", str(output_dir),
            "--compression", str(args.compression)
        ]
        
        if args.max_episodes:
            episode_cmd.extend(["--max_episodes", str(args.max_episodes)])
        
        if not run_command(episode_cmd, "Episode generation"):
            logger.error("Episode generation failed")
            return 1
        
        # 步骤4: 生成总结报告
        logger.info("=" * 50)
        logger.info("STEP 4: Generating Summary Report")
        logger.info("=" * 50)
        
        # 读取结果文件
        episodes_file = output_dir / "episodes.json"
        if episodes_file.exists():
            import json
            with open(episodes_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # 生成摘要
            total_episodes = results.get("total_episodes", 0)
            quality_summary = results.get("quality_summary", {})
            
            summary_lines = [
                "# Novel Rewrite Pipeline Summary",
                "",
                f"**Input File**: {args.input_file}",
                f"**Output Directory**: {args.output_dir}",
                f"**Compression Ratio**: {args.compression}",
                "",
                "## Results",
                f"- **Total Episodes Generated**: {total_episodes}",
                f"- **Quality Distribution**:",
                f"  - Green: {quality_summary.get('green', 0)}",
                f"  - Yellow: {quality_summary.get('yellow', 0)}",
                f"  - Red: {quality_summary.get('red', 0)}",
                "",
                "## Output Files",
                f"- Spine Events: `{spine_file.name}`",
                f"- Phase Allocation: `{phase_file.name}`",
                f"- Episodes: `{episodes_file.name}`",
                f"- Individual Scripts: `episode_*.txt`",
                "",
                "## Next Steps",
                "1. Review quality reports for any Yellow/Red episodes",
                "2. Manual review and editing if needed",
                "3. Proceed to video production pipeline",
                "",
                f"*Generated by novel_rewrite pipeline*"
            ]
            
            summary_file = output_dir / "pipeline_summary.md"
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(summary_lines))
            
            logger.info(f"Summary report saved to {summary_file}")
        
        # 最终统计
        logger.info("=" * 50)
        logger.info("PIPELINE COMPLETED SUCCESSFULLY")
        logger.info("=" * 50)
        
        # 列出输出文件
        output_files = list(output_dir.glob("*"))
        logger.info(f"Generated {len(output_files)} output files:")
        for file_path in sorted(output_files):
            if file_path.is_file():
                size_kb = file_path.stat().st_size / 1024
                logger.info(f"  {file_path.name} ({size_kb:.1f} KB)")
        
        return 0
        
    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
