# 模型配置文件

# LLM配置
llm:
  provider: "openai"
  model: "gpt-4"
  temperature: 0.7
  max_tokens: 4000
  timeout: 60

# 向量模型配置
embedding:
  models:
    - name: "xiaobu-embedding/gte-Qwen2-1.5B-instruct"
      dimension: 1536
      max_length: 512
    - name: "mxbai-embed-large"
      dimension: 1024
      max_length: 512

# 质量控制参数
quality:
  thresholds:
    base: 0.45
    max: 0.55
    yellow: 0.5
    red: 0.7
  
  yellow_queue:
    max_retry: 3
    auto_pause_rate: 0.15

# 处理参数
processing:
  spine:
    max_events_per_chapter: 4
    min_importance: 0.3
    min_tension: 0.2
  
  phase:
    target_ratios:
      setup: 0.1
      development: 0.4
      climax: 0.3
      resolution: 0.2
    tolerance: 0.1
  
  episode:
    target_duration: 12.0
    max_scenes: 6
    min_scenes: 4

# 缓存配置
cache:
  memory_limit: 1000
  cleanup_days: 30
  max_size_mb: 1024

# GitHub集成
github:
  labels:
    quality_issue: "quality-issue"
    auto_generated: "auto-generated"
    severity_high: "severity-high"
    severity_medium: "severity-medium"
    severity_low: "severity-low"
