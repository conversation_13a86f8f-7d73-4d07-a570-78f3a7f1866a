"""
统一I/O操作模块
"""

import json
import os
import logging
from typing import Any, Dict, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

def ensure_dir(file_path: str) -> None:
    """确保目录存在"""
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)

def load_json(file_path: str) -> Dict[str, Any]:
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"JSON file not found: {file_path}")
        raise
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in file {file_path}: {e}")
        raise
    except Exception as e:
        logger.error(f"Failed to load JSON from {file_path}: {e}")
        raise

def save_json(data: Any, file_path: str, indent: int = 2) -> None:
    """保存数据为JSON文件"""
    try:
        ensure_dir(file_path)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=indent)
        logger.debug(f"Saved JSON to {file_path}")
    except Exception as e:
        logger.error(f"Failed to save JSON to {file_path}: {e}")
        raise

def load_text(file_path: str) -> str:
    """加载文本文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        logger.error(f"Text file not found: {file_path}")
        raise
    except Exception as e:
        logger.error(f"Failed to load text from {file_path}: {e}")
        raise

def save_text(text: str, file_path: str) -> None:
    """保存文本到文件"""
    try:
        ensure_dir(file_path)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(text)
        logger.debug(f"Saved text to {file_path}")
    except Exception as e:
        logger.error(f"Failed to save text to {file_path}: {e}")
        raise

def load_lines(file_path: str) -> List[str]:
    """加载文本文件为行列表"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return [line.rstrip('\n\r') for line in f]
    except Exception as e:
        logger.error(f"Failed to load lines from {file_path}: {e}")
        raise

def save_lines(lines: List[str], file_path: str) -> None:
    """保存行列表到文件"""
    try:
        ensure_dir(file_path)
        with open(file_path, 'w', encoding='utf-8') as f:
            for line in lines:
                f.write(line + '\n')
        logger.debug(f"Saved {len(lines)} lines to {file_path}")
    except Exception as e:
        logger.error(f"Failed to save lines to {file_path}: {e}")
        raise

def list_files(directory: str, pattern: str = "*", recursive: bool = False) -> List[str]:
    """列出目录中的文件"""
    try:
        path = Path(directory)
        if recursive:
            files = list(path.rglob(pattern))
        else:
            files = list(path.glob(pattern))
        
        return [str(f) for f in files if f.is_file()]
    except Exception as e:
        logger.error(f"Failed to list files in {directory}: {e}")
        return []

def file_exists(file_path: str) -> bool:
    """检查文件是否存在"""
    return os.path.isfile(file_path)

def dir_exists(dir_path: str) -> bool:
    """检查目录是否存在"""
    return os.path.isdir(dir_path)

def get_file_size(file_path: str) -> int:
    """获取文件大小（字节）"""
    try:
        return os.path.getsize(file_path)
    except Exception as e:
        logger.error(f"Failed to get size of {file_path}: {e}")
        return 0

def copy_file(src: str, dst: str) -> None:
    """复制文件"""
    import shutil
    try:
        ensure_dir(dst)
        shutil.copy2(src, dst)
        logger.debug(f"Copied {src} to {dst}")
    except Exception as e:
        logger.error(f"Failed to copy {src} to {dst}: {e}")
        raise

def move_file(src: str, dst: str) -> None:
    """移动文件"""
    import shutil
    try:
        ensure_dir(dst)
        shutil.move(src, dst)
        logger.debug(f"Moved {src} to {dst}")
    except Exception as e:
        logger.error(f"Failed to move {src} to {dst}: {e}")
        raise

def delete_file(file_path: str) -> None:
    """删除文件"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.debug(f"Deleted {file_path}")
    except Exception as e:
        logger.error(f"Failed to delete {file_path}: {e}")
        raise

class FileManager:
    """文件管理器类"""
    
    def __init__(self, base_dir: str = "."):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
    
    def get_path(self, relative_path: str) -> str:
        """获取相对于基础目录的完整路径"""
        return str(self.base_dir / relative_path)
    
    def load_json(self, relative_path: str) -> Dict[str, Any]:
        """加载JSON文件"""
        return load_json(self.get_path(relative_path))
    
    def save_json(self, data: Any, relative_path: str, indent: int = 2) -> None:
        """保存JSON文件"""
        save_json(data, self.get_path(relative_path), indent)
    
    def load_text(self, relative_path: str) -> str:
        """加载文本文件"""
        return load_text(self.get_path(relative_path))
    
    def save_text(self, text: str, relative_path: str) -> None:
        """保存文本文件"""
        save_text(text, self.get_path(relative_path))
    
    def list_files(self, pattern: str = "*", recursive: bool = False) -> List[str]:
        """列出文件"""
        return list_files(str(self.base_dir), pattern, recursive)
    
    def cleanup_old_files(self, pattern: str, max_age_days: int = 30) -> int:
        """清理旧文件"""
        import time
        
        files = self.list_files(pattern, recursive=True)
        current_time = time.time()
        deleted_count = 0
        
        for file_path in files:
            try:
                file_age = current_time - os.path.getmtime(file_path)
                if file_age > max_age_days * 24 * 3600:
                    delete_file(file_path)
                    deleted_count += 1
            except Exception as e:
                logger.warning(f"Failed to check/delete {file_path}: {e}")
        
        logger.info(f"Cleaned up {deleted_count} old files")
        return deleted_count
