#!/usr/bin/env python3
"""
转换现有章节摘要格式并运行novel_rewrite流水线
"""

import json
import sys
import os
import argparse
from pathlib import Path
import subprocess
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def convert_chapter_format(input_file: str, output_file: str) -> bool:
    """
    将现有的章节摘要格式转换为novel_rewrite期望的格式
    
    Args:
        input_file: 输入文件路径（现有格式）
        output_file: 输出文件路径（转换后格式）
    
    Returns:
        bool: 转换是否成功
    """
    try:
        logger.info(f"Converting {input_file} to {output_file}")
        
        # 读取现有格式
        with open(input_file, 'r', encoding='utf-8') as f:
            chapters_list = json.load(f)
        
        if not isinstance(chapters_list, list):
            logger.error("Input file should contain a list of chapters")
            return False
        
        # 转换为期望格式
        converted_data = {
            "novel_info": {
                "title": "放开那个女巫",
                "author": "二目",
                "genre": "奇幻",
                "description": "现代工程师穿越到异世界，利用现代知识改变世界的故事"
            },
            "chapters": chapters_list
        }
        
        # 保存转换后的数据
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Successfully converted {len(chapters_list)} chapters")
        return True
        
    except Exception as e:
        logger.error(f"Conversion failed: {e}")
        return False

def run_novel_rewrite_pipeline(input_file: str, output_dir: str, max_episodes: int = None) -> bool:
    """
    运行novel_rewrite流水线
    
    Args:
        input_file: 转换后的输入文件
        output_dir: 输出目录
        max_episodes: 最大集数限制
    
    Returns:
        bool: 流水线是否成功
    """
    try:
        logger.info("Running novel_rewrite pipeline...")
        
        # 构建命令
        cmd = [
            sys.executable, "full_flow.py",
            input_file,
            "--output_dir", output_dir
        ]
        
        if max_episodes:
            cmd.extend(["--max_episodes", str(max_episodes)])
        
        # 运行流水线
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("Pipeline completed successfully")
            return True
        else:
            logger.error(f"Pipeline failed with return code {result.returncode}")
            logger.error(f"STDOUT: {result.stdout}")
            logger.error(f"STDERR: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Failed to run pipeline: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Convert and run novel_rewrite pipeline")
    parser.add_argument("input_file", help="Original chapter summaries file")
    parser.add_argument("--output_dir", default="./output_rewrite", help="Output directory")
    parser.add_argument("--max_episodes", type=int, default=2, help="Maximum episodes to generate")
    parser.add_argument("--keep_converted", action="store_true", help="Keep converted input file")
    
    args = parser.parse_args()
    
    try:
        # 创建输出目录
        output_dir = Path(args.output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # 转换输入文件
        converted_file = output_dir / "converted_input.json"
        if not convert_chapter_format(args.input_file, str(converted_file)):
            logger.error("Format conversion failed")
            return 1
        
        # 运行流水线
        if not run_novel_rewrite_pipeline(str(converted_file), args.output_dir, args.max_episodes):
            logger.error("Pipeline execution failed")
            return 1
        
        # 清理转换文件（除非指定保留）
        if not args.keep_converted:
            converted_file.unlink()
            logger.info("Cleaned up converted input file")
        
        logger.info("=" * 50)
        logger.info("CONVERSION AND PIPELINE COMPLETED SUCCESSFULLY")
        logger.info("=" * 50)
        
        # 列出输出文件
        output_files = list(output_dir.glob("*"))
        logger.info(f"Generated {len(output_files)} output files:")
        for file_path in sorted(output_files):
            if file_path.is_file():
                size_kb = file_path.stat().st_size / 1024
                logger.info(f"  {file_path.name} ({size_kb:.1f} KB)")
        
        return 0
        
    except Exception as e:
        logger.error(f"Main process failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
