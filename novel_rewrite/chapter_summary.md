# 章节摘要生成提示词

## 任务描述
从原始小说章节中生成结构化摘要，为后续的Spine事件抽取和剧集改编提供基础。

## 输入格式
- 章节原文（可能很长）
- 章节基本信息（章节号、标题等）

## 输出要求

### 1. 基本信息
```json
{
  "basic_info": {
    "chapter_number": 1,
    "title": "章节标题",
    "word_count": 3500,
    "estimated_reading_time": 12
  }
}
```

### 2. 叙述摘要
```json
{
  "narrative": {
    "content": "简洁的章节内容摘要，保留关键情节和角色动作，控制在200-300字",
    "tone": "紧张/轻松/悲伤/激昂",
    "setting": "主要场景描述"
  }
}
```

### 3. 关键事件
```json
{
  "key_events": [
    {
      "event": "具体事件描述",
      "importance": 0.8,
      "characters_involved": ["角色1", "角色2"],
      "consequences": "事件后果"
    }
  ]
}
```

### 4. 角色信息
```json
{
  "key_characters": ["主要角色列表"],
  "character_developments": {
    "角色名": "该角色在本章的发展变化"
  }
}
```

## 质量标准

### 摘要质量
- 忠实原文，不添加原文没有的内容
- 突出关键情节转折点
- 保持角色动机的逻辑性
- 语言简洁明了

### 事件抽取
- 重要性评分要客观准确
- 事件描述要具体可操作
- 避免过于细节化的描述
- 关注对后续情节的影响

### 角色处理
- 统一角色称呼（使用标准名称）
- 记录角色关系变化
- 突出角色成长弧线
- 注意角色动机的合理性

## 注意事项

1. **保持客观性**: 不要添加主观解读或推测
2. **控制长度**: 摘要要简洁，避免冗余
3. **突出重点**: 重点关注推动情节的关键事件
4. **保持一致性**: 角色名称和设定要与前面章节保持一致
