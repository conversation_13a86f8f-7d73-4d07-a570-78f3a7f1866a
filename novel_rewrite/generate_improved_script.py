#!/usr/bin/env python3
"""
生成改进版剧本，解决背景介绍和人物介绍问题
"""

import json
import sys
import os
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_paths():
    """设置路径"""
    main_project_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    if main_project_path not in sys.path:
        sys.path.insert(0, main_project_path)

def generate_improved_episode_script(spine_events, episode_num, output_dir):
    """生成改进版剧集脚本，包含背景介绍和人物介绍"""
    setup_paths()
    from modules.langchain_interface import call_llm
    
    # 构建改进的episode_structure
    episode_structure = {
        "episode_number": episode_num,
        "title": f"第{episode_num}集",
        "main_plots": [event.get('text', '') for event in spine_events[:3]],
        "main_conflict": "现代思维与传统观念的冲突",
        "characters": ["罗兰", "安娜", "巴罗夫", "卡特"],
        "acts": {
            "opening": {"purpose": "建立情境", "scenes": 1},
            "development": {"purpose": "推进冲突", "scenes": 1},
            "climax_resolution": {"purpose": "解决冲突", "scenes": 1}
        }
    }
    
    # 构建改进的prompt_data，强调背景介绍和人物介绍
    prompt_data = {
        "episode_structure": episode_structure,
        "global_outline": {
            "theme": "现代工程师穿越异世界的故事",
            "setting": "中世纪奇幻世界，边陲镇",
            "background": "主角罗兰是现代工程师，穿越成为四王子，面临女巫处决的道德抉择"
        },
        "previous_episode_script": "",
        "chapter_summaries": spine_events,
        "style": "快节奏，注重背景介绍和人物介绍",
        "language": "中文",
        "world_tone": "奇幻",
        "main_conflict": "现代思维与传统观念的冲突",
        "special_requirements": {
            "background_narration": "必须在开头添加详细的背景旁白，说明世界设定、时代背景和当前情况",
            "character_introduction": "每个新出场的角色都必须有明确的身份介绍，包括姓名、职位、与主角的关系",
            "narrative_style": "使用旁白来补充背景信息，确保观众能理解故事背景和人物关系"
        }
    }
    
    try:
        logger.info(f"Generating improved episode {episode_num} script...")
        
        # 调用LLM生成改进版剧本
        response = call_llm(
            api_function="generate_full_script",
            prompt_data=prompt_data,
            using_cache=False  # 不使用缓存，确保生成新内容
        )
        
        if response:
            # 保存原始响应
            script_file = output_dir / f"episode_{episode_num:02d}_improved_script.txt"
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(response)
            
            # 转换为JSON格式
            episode_json = convert_improved_script_to_json(response, episode_num)
            
            json_file = output_dir / f"episode_{episode_num:02d}_improved.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(episode_json, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Improved episode {episode_num} generated successfully")
            return True
        else:
            logger.error(f"Failed to generate improved episode {episode_num}")
            return False
            
    except Exception as e:
        logger.error(f"Error generating improved episode {episode_num}: {e}")
        return False

def convert_improved_script_to_json(script_text, episode_num):
    """将改进版脚本转换为JSON格式，特别处理旁白和人物介绍"""
    
    lines = script_text.split('\n')
    scenes = []
    current_scene = None
    dialogue = []
    scene_counter = 0
    introduced_characters = set()  # 跟踪已介绍的角色
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('———') or line.startswith('【第') or line == '【剧终钩子】':
            continue
            
        # 检测场景开始
        if (line.startswith('【') and ('·' in line or '场景' in line)) or \
           line.startswith('——场景') or \
           (line.startswith('广场') or line.startswith('王宫') or line.startswith('地牢') or \
            line.startswith('边陲镇') or line.startswith('城堡')):
            
            # 保存上一个场景
            if current_scene and dialogue:
                current_scene['dialogue'] = dialogue
                scenes.append(current_scene)
            
            scene_counter += 1
            location = "边陲镇"
            
            # 确定场景位置
            if '地牢' in line:
                location = "地牢"
            elif '王宫' in line or '寝室' in line or '寝宫' in line:
                location = "王宫"
            elif '书房' in line:
                location = "书房"
            elif '广场' in line or '刑场' in line:
                location = "广场"
            elif '城堡' in line:
                location = "城堡"
            elif '实验室' in line:
                location = "实验室"
            
            current_scene = {
                "scene_number": scene_counter,
                "n": scene_counter,
                "sn": scene_counter,
                "shot_type": "dialogue",
                "environment": {"image": location},
                "narration": {"nr": line},
                "sound_cues": []
            }
            dialogue = []
            continue
            
        # 检测对话
        if '：' in line and not line.startswith('【'):
            parts = line.split('：', 1)
            if len(parts) == 2:
                speaker = parts[0].strip()
                text = parts[1].strip()
                
                # 跳过场景分隔符
                if speaker.startswith('——'):
                    continue
                
                # 处理人物介绍
                original_speaker = speaker
                if '（' in speaker and '）' in speaker:
                    speaker_name = speaker.split('（')[0].strip()
                else:
                    speaker_name = speaker
                
                # 如果是新角色首次出场，添加介绍
                if speaker_name not in introduced_characters and speaker_name not in ['旁白', '环境音', '特效音']:
                    character_intro = get_character_introduction(speaker_name, episode_num)
                    if character_intro:
                        dialogue.append({
                            "c": "【旁白",
                            "m": "Neutral",
                            "t": character_intro
                        })
                        introduced_characters.add(speaker_name)
                
                # 确定情绪
                mood = "Neutral"
                if '（' in original_speaker and '）' in original_speaker:
                    mood_text = original_speaker[original_speaker.find('（')+1:original_speaker.find('）')]
                    speaker = original_speaker.split('（')[0].strip()
                    
                    # 映射情绪
                    mood_mapping = {
                        '低声': 'Worried', '急切': 'Urgent', '迟疑': 'Hesitant',
                        '压低声音': 'Worried', '小声': 'Quiet', '冷静': 'Calm',
                        '平和': 'Peaceful', '坚定': 'Determined', '心念': 'Thoughtful',
                        '内心': 'Thoughtful', '心想': 'Thoughtful', '斩钉截铁': 'Determined',
                        '打断': 'Interrupting', '警告': 'Warning', '不悦': 'Displeased',
                        '带刺': 'Sarcastic', '虚弱': 'Weak', '喉咙哽咽': 'Emotional',
                        '颤抖': 'Trembling', '仓促进来': 'Rushed', '盯着': 'Focused'
                    }
                    
                    mood = mood_mapping.get(mood_text, 'Neutral')
                    
                    # 处理内心独白
                    if mood_text in ['心念', '内心', '心想']:
                        speaker = speaker + "（内心独白）"
                
                dialogue.append({
                    "c": speaker,
                    "m": mood,
                    "t": text
                })
        
        # 检测环境描述和特效
        elif line.startswith('【') and line.endswith('】'):
            content = line[1:-1]
            if '场景' in content:
                continue  # 跳过场景标记
            dialogue.append({
                "c": "【环境音",
                "m": "Neutral",
                "t": content
            })
        elif line.startswith('——') and ('安娜' in line or '火焰' in line):
            dialogue.append({
                "c": "【特效音",
                "m": "Neutral",
                "t": "火焰燃烧，金属熔化的声音"
            })
        elif line.startswith('（') and line.endswith('）') and '内心独白' in line:
            dialogue.append({
                "c": "【旁白",
                "m": "Neutral",
                "t": line[1:-1]
            })
    
    # 添加最后一个场景
    if current_scene and dialogue:
        current_scene['dialogue'] = dialogue
        scenes.append(current_scene)
    
    # 在第一个场景开头添加背景介绍
    if scenes and episode_num == 1:
        background_intro = get_background_introduction()
        scenes[0]['dialogue'].insert(0, {
            "c": "【旁白",
            "m": "Neutral",
            "t": background_intro
        })
    
    # 构建完整结构
    characters = [
        {"name": "罗兰", "gender": "male", "age": "Adult", "role": ["主角"], "aliases": []},
        {"name": "安娜", "gender": "female", "age": "Young", "role": ["女巫"], "aliases": []},
        {"name": "巴罗夫", "gender": "male", "age": "Adult", "role": ["助手"], "aliases": []},
        {"name": "卡特", "gender": "male", "age": "Adult", "role": ["骑士"], "aliases": []}
    ]
    
    return {
        "ep": {
            "ep_n": episode_num,
            "t": f"第{episode_num}集",
            "c": characters,
            "scenes": scenes
        },
        "episode_structure": {
            "episode_number": episode_num,
            "main_plots": ["穿越与身份认知", "女巫安娜的救赎", "传统与现代的冲突"],
            "main_conflict": "现代思维与传统观念的冲突",
            "characters": [char["name"] for char in characters],
            "acts": {
                "opening": {"purpose": "建立情境和冲突", "scenes": len(scenes)//3 or 1},
                "development": {"purpose": "推进主要情节", "scenes": len(scenes)//3 or 1},
                "ending": {"purpose": "解决冲突并铺垫", "scenes": len(scenes)//3 or 1}
            }
        }
    }

def get_background_introduction():
    """获取背景介绍旁白"""
    return ("这里是格雷卡斯特王国的边陲镇，一个远离王都的偏远领地。在这个魔法与科学并存的世界里，"
            "女巫被视为邪恶的存在，一旦发现必须处以火刑。今天，又有一名女巫即将在广场上被执行死刑。"
            "而做出最终决定的，是刚刚穿越到这个世界的现代工程师——四王子罗兰。")

def get_character_introduction(character_name, episode_num):
    """获取角色介绍"""
    introductions = {
        "巴罗夫": "巴罗夫，四王子罗兰的贴身助手，负责处理领地的日常事务，是一个经验丰富但思想保守的中年男子。",
        "卡特": "卡特，四王子的护卫队长，忠诚可靠，负责保护王子的安全，但严格按照传统行事。",
        "安娜": "安娜，一个年轻的女巫，拥有控制火焰的能力，因为救助他人时暴露了身份而被捕。",
        "罗兰": None  # 主角不需要介绍
    }
    
    return introductions.get(character_name)

def main():
    try:
        # 创建输出目录
        output_dir = Path("./output_improved")
        output_dir.mkdir(exist_ok=True)
        
        # 读取已生成的spine事件
        spine_file = Path("./output_llm_quick/spine_events.json")
        if not spine_file.exists():
            logger.error("Spine events file not found. Please run generate_llm_episodes_quick.py first.")
            return 1
        
        with open(spine_file, 'r', encoding='utf-8') as f:
            spine_results = json.load(f)
        
        logger.info(f"Loaded spine events for {len(spine_results)} chapters")
        
        # 生成改进版第1集（章节1-4）
        episode_1_events = []
        for ch_num in range(1, 5):
            if str(ch_num) in spine_results:
                episode_1_events.extend(spine_results[str(ch_num)]["spine_events"])
        
        success_1 = generate_improved_episode_script(episode_1_events, 1, output_dir)
        
        # 生成改进版第2集（章节5-8）
        episode_2_events = []
        for ch_num in range(5, 9):
            if str(ch_num) in spine_results:
                episode_2_events.extend(spine_results[str(ch_num)]["spine_events"])
        
        success_2 = generate_improved_episode_script(episode_2_events, 2, output_dir)
        
        logger.info("=" * 50)
        logger.info("IMPROVED SCRIPT GENERATION COMPLETED")
        logger.info("=" * 50)
        
        success_count = sum([success_1, success_2])
        logger.info(f"Generated {success_count} improved episodes successfully")
        logger.info(f"Output directory: {output_dir}")
        
        if success_count > 0:
            logger.info("\n改进内容:")
            logger.info("1. 添加了详细的背景介绍旁白")
            logger.info("2. 为每个新出场角色添加了身份介绍")
            logger.info("3. 增强了故事的可理解性和沉浸感")
        
        return 0 if success_count > 0 else 1
        
    except Exception as e:
        logger.error(f"Generation failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
