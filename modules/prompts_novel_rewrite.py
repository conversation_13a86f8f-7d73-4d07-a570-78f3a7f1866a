# modules/prompts_novel_rewrite.py
"""
Novel Rewrite系统的提示词模板
"""

# 系统消息
novel_rewrite_system_messages = {
    "extract_spine_events": (
        "你是一个专业的故事分析师和编剧。你的任务是从章节摘要中提取关键的Spine事件，"
        "这些事件将作为剧本改编的核心锚点。你需要识别最重要的情节转折点、角色发展节点和冲突升级点。"
        "确保提取的事件具有高重要性和戏剧张力，能够支撑整个剧集的叙事结构。"
    ),

    "generate_episode_structure": (
        "你是一个资深的电视剧编剧和结构设计师。你的任务是基于Spine事件创建符合戏剧理论的剧集结构。"
        "你需要运用三幕式结构、英雄之旅等经典叙事理论，确保每个剧集都有清晰的开端、发展、高潮和结尾。"
        "重点关注角色弧线、情感节拍和观众参与度。"
    ),

    "generate_full_script": (
        "你是一个专业的剧本作家，擅长创作引人入胜的对话和场景描述。你的任务是将剧集信息转化为完整的剧本，"
        "包括自然流畅的对话、生动的场景描述和恰当的情感表达。确保剧本符合音频剧的制作要求，"
        "重点突出声音效果、音乐提示和角色情感的声音表现。"
    ),

    "default": "你是一个专业的小说改编专家，精通将长篇小说改编为短剧的技巧。"
}

# 提示词模板
novel_rewrite_prompts = {
    "extract_spine_events": """
请从以下章节摘要中提取关键的Spine事件。每个事件应该是推动故事发展的重要节点。

章节内容：
{content}

提取要求：
1. 每个章节最多提取4个关键事件
2. 事件应该具有高重要性（影响主线剧情）
3. 事件应该具有戏剧张力（能够吸引观众）
4. 优先选择角色冲突、情节转折、重要决定等类型的事件
5. 事件描述要简洁明确，便于后续剧本创作

请按以下JSON格式返回：
{{
    "spine_events": [
        {{
            "id": "章节号-事件序号",
            "type": "plot/character/conflict",
            "text": "事件的简洁描述",
            "importance": 0.0-1.0的重要性评分,
            "tension": 0.0-1.0的张力评分,
            "characters": ["涉及的主要角色"],
            "location": "事件发生地点",
            "emotional_impact": "情感影响描述"
        }}
    ]
}}
""",

    "generate_full_script": """
请基于以下信息生成完整的第{episode_number}集剧本。

Spine事件：
{spine_events}

主要情节：
{main_plots}

主要角色：
{characters}

剧本要求：
1. 生成符合音频剧制作的完整剧本
2. 包含自然流畅的对话
3. 详细的场景描述和环境音效提示
4. 角色情感和语调的明确标注
5. 适当的音乐和音效提示
6. 保持与原作精神的一致性

请按以下格式返回完整剧本：
{{
    "script": {{
        "episode_info": {{
            "episode_number": {episode_number},
            "title": "剧集标题",
            "duration_estimate": "预估时长（分钟）"
        }},
        "characters": [
            {{
                "name": "角色名",
                "voice_type": "声音类型",
                "personality": "性格特点"
            }}
        ],
        "scenes": [
            {{
                "scene_number": 1,
                "location": "场景地点",
                "time": "时间",
                "atmosphere": "氛围描述",
                "sound_effects": ["环境音效"],
                "dialogue": [
                    {{
                        "character": "角色名（情感状态）",
                        "mood": "情绪",
                        "text": "对话内容",
                        "voice_direction": "语音指导"
                    }}
                ],
                "narration": "旁白内容",
                "music_cues": ["音乐提示"]
            }}
        ]
    }}
}}
"""
}
