#!/usr/bin/env python3
"""
测试剧情驱动的快节奏剧本生成
专门检查内心独白比例和剧情推进效率
"""

import os
import sys
import json
import re
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import logger, ANIMATION_DRAMA_DIR
from modules.langchain_interface import call_llm
from modules.gpt_parameters import (
    GENERATE_FULL_SCRIPT_LLM_TYPE,
    GENERATE_FULL_SCRIPT_MODEL_KEY
)

def analyze_content_ratios(script_content: str) -> dict:
    """分析剧本内容比例"""
    
    lines = script_content.split('\n')
    total_lines = len([line for line in lines if line.strip()])
    
    # 识别不同类型的内容
    dialogue_lines = []
    monologue_lines = []
    description_lines = []
    transition_lines = []
    
    current_monologue = []
    in_monologue = False
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 检测内心独白开始
        if '——' in line and ('独白' in line or '内心' in line):
            in_monologue = True
            current_monologue = [line]
            continue
        
        # 检测内心独白结束
        if in_monologue and ('——' in line or line.startswith('（') or line.startswith('【')):
            if current_monologue:
                monologue_lines.extend(current_monologue)
                current_monologue = []
            in_monologue = False
        
        if in_monologue:
            current_monologue.append(line)
            continue
        
        # 对话行（包含冒号且不是场景标记）
        if '：' in line and not line.startswith('【') and not line.startswith('（'):
            dialogue_lines.append(line)
        # 场景转换
        elif line.startswith('【') or line.startswith('—————'):
            transition_lines.append(line)
        # 环境描述
        elif line.startswith('（') or '环境音' in line:
            description_lines.append(line)
        # 其他描述性内容
        else:
            description_lines.append(line)
    
    # 添加剩余的内心独白
    if current_monologue:
        monologue_lines.extend(current_monologue)
    
    # 计算比例
    dialogue_ratio = len(dialogue_lines) / total_lines if total_lines > 0 else 0
    monologue_ratio = len(monologue_lines) / total_lines if total_lines > 0 else 0
    description_ratio = len(description_lines) / total_lines if total_lines > 0 else 0
    transition_ratio = len(transition_lines) / total_lines if total_lines > 0 else 0
    
    return {
        "total_lines": total_lines,
        "dialogue_lines": len(dialogue_lines),
        "monologue_lines": len(monologue_lines),
        "description_lines": len(description_lines),
        "transition_lines": len(transition_lines),
        "dialogue_ratio": dialogue_ratio,
        "monologue_ratio": monologue_ratio,
        "description_ratio": description_ratio,
        "transition_ratio": transition_ratio,
        "sample_monologue": monologue_lines[:3] if monologue_lines else [],
        "sample_dialogue": dialogue_lines[:3] if dialogue_lines else []
    }

def check_plot_advancement(script_content: str) -> dict:
    """检查剧情推进效率"""
    
    # 识别剧情推进关键词
    plot_keywords = [
        '决定', '选择', '命令', '拒绝', '同意', '发现', '揭示', '威胁', 
        '冲突', '对抗', '逃跑', '追捕', '救援', '背叛', '联盟', '计划',
        '秘密', '真相', '危险', '机会', '变化', '转折', '结果', '后果'
    ]
    
    # 识别哲学/反思关键词（应该避免的）
    philosophical_keywords = [
        '思考', '反思', '哲学', '人生', '意义', '存在', '本质', '价值',
        '命运', '宿命', '感慨', '感叹', '回忆', '往昔', '曾经', '过去'
    ]
    
    plot_count = sum(script_content.count(keyword) for keyword in plot_keywords)
    philosophical_count = sum(script_content.count(keyword) for keyword in philosophical_keywords)
    
    # 检查场景数量和转换
    scene_markers = re.findall(r'【场景.*?】', script_content)
    scene_count = len(scene_markers)
    
    # 检查对话中的冲突
    dialogue_lines = re.findall(r'.*?：.*', script_content)
    conflict_dialogue = [line for line in dialogue_lines 
                        if any(word in line for word in ['但是', '不过', '然而', '可是', '难道', '为什么', '怎么能'])]
    
    return {
        "plot_advancement_score": plot_count,
        "philosophical_content_score": philosophical_count,
        "scene_count": scene_count,
        "total_dialogue_lines": len(dialogue_lines),
        "conflict_dialogue_lines": len(conflict_dialogue),
        "conflict_ratio": len(conflict_dialogue) / len(dialogue_lines) if dialogue_lines else 0,
        "plot_vs_philosophy_ratio": plot_count / (philosophical_count + 1)  # +1 to avoid division by zero
    }

def generate_plot_driven_test():
    """生成剧情驱动的测试剧本"""
    
    logger.info("生成剧情驱动的测试剧本...")
    
    test_data = {
        "episode_structure": {
            "episode_number": 1,
            "main_plots": ["罗兰必须立即决定是否处决女巫", "巴罗夫和卡特的权力挑战"],
            "main_conflict": "生死抉择的紧急时刻",
            "characters": ["罗兰", "巴罗夫", "卡特"],
            "acts": {
                "opening": {
                    "purpose": "立即进入生死抉择",
                    "scenes": 1,
                    "setup_elements": ["处刑台前的紧急决定"]
                },
                "development": {
                    "purpose": "权力对抗升级",
                    "scenes": 2,
                    "escalation_points": ["公开对抗", "威胁与反威胁"]
                },
                "ending": {
                    "purpose": "决定后果和新危机",
                    "scenes": 1,
                    "resolutions": ["暂时胜利但新威胁出现"]
                }
            }
        },
        "global_outline": {"main_theme": "权力斗争", "tone": "紧张激烈"},
        "previous_episode_script": "",
        "chapter_summaries": [{"summary": "紧急决策时刻", "key_events": ["生死抉择"]}],
        "style": "快节奏剧情驱动",
        "language": "Chinese",
        "world_tone": "紧张",
        "main_conflict": "生死抉择的紧急时刻"
    }
    
    try:
        response = call_llm(
            api_function="generate_full_script",
            prompt_data=test_data,
            llm_type=GENERATE_FULL_SCRIPT_LLM_TYPE,
            model_key=GENERATE_FULL_SCRIPT_MODEL_KEY,
            using_cache=False
        )
        
        if response:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = Path(ANIMATION_DRAMA_DIR) / "temp" / f"plot_driven_test_{timestamp}.txt"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(response)
            
            logger.info(f"剧情驱动测试剧本已生成: {output_file}")
            
            # 分析结果
            analyze_plot_driven_result(response, output_file)
            
            return response
        else:
            logger.error("生成失败：无响应")
            return None
            
    except Exception as e:
        logger.error(f"生成时出错: {e}")
        return None

def analyze_plot_driven_result(script_content: str, output_file: Path):
    """分析剧情驱动测试结果"""
    
    print("\n" + "="*70)
    print("剧情驱动剧本分析结果")
    print("="*70)
    
    # 内容比例分析
    ratios = analyze_content_ratios(script_content)
    
    print(f"\n📊 内容比例分析:")
    print(f"  总行数: {ratios['total_lines']}")
    print(f"  对话行数: {ratios['dialogue_lines']} ({ratios['dialogue_ratio']:.1%})")
    print(f"  内心独白: {ratios['monologue_lines']} ({ratios['monologue_ratio']:.1%})")
    print(f"  描述内容: {ratios['description_lines']} ({ratios['description_ratio']:.1%})")
    print(f"  场景转换: {ratios['transition_lines']} ({ratios['transition_ratio']:.1%})")
    
    # 目标检查
    print(f"\n🎯 目标达成检查:")
    dialogue_ok = ratios['dialogue_ratio'] >= 0.7
    monologue_ok = ratios['monologue_ratio'] <= 0.1
    
    print(f"  对话比例 ≥70%: {'✅' if dialogue_ok else '❌'} ({ratios['dialogue_ratio']:.1%})")
    print(f"  内心独白 ≤10%: {'✅' if monologue_ok else '❌'} ({ratios['monologue_ratio']:.1%})")
    
    # 剧情推进分析
    plot_analysis = check_plot_advancement(script_content)
    
    print(f"\n🚀 剧情推进分析:")
    print(f"  剧情推进关键词: {plot_analysis['plot_advancement_score']} 个")
    print(f"  哲学反思内容: {plot_analysis['philosophical_content_score']} 个")
    print(f"  场景数量: {plot_analysis['scene_count']}")
    print(f"  冲突对话比例: {plot_analysis['conflict_ratio']:.1%}")
    print(f"  剧情vs哲学比例: {plot_analysis['plot_vs_philosophy_ratio']:.1f}:1")
    
    # 质量评估
    print(f"\n⭐ 整体质量评估:")
    
    score = 0
    if dialogue_ok: score += 30
    if monologue_ok: score += 30
    if plot_analysis['plot_advancement_score'] > 10: score += 20
    if plot_analysis['conflict_ratio'] > 0.3: score += 10
    if plot_analysis['scene_count'] >= 3: score += 10
    
    print(f"  总分: {score}/100")
    
    if score >= 80:
        print(f"  评级: 🌟🌟🌟 优秀 - 达到快节奏要求")
    elif score >= 60:
        print(f"  评级: 🌟🌟 良好 - 基本达到要求")
    else:
        print(f"  评级: 🌟 需要改进 - 未达到快节奏要求")
    
    # 改进建议
    print(f"\n💡 改进建议:")
    if not dialogue_ok:
        print(f"  - 增加对话比例，当前仅{ratios['dialogue_ratio']:.1%}，目标70%+")
    if not monologue_ok:
        print(f"  - 大幅减少内心独白，当前{ratios['monologue_ratio']:.1%}，目标≤10%")
    if plot_analysis['philosophical_content_score'] > 5:
        print(f"  - 减少哲学反思内容，专注剧情推进")
    if plot_analysis['conflict_ratio'] < 0.3:
        print(f"  - 增加对话中的冲突和对抗")
    
    print(f"\n📁 完整剧本: {output_file}")

def main():
    """主函数"""
    
    print("🎬 开始测试剧情驱动的快节奏剧本生成...")
    
    result = generate_plot_driven_test()
    
    if result:
        print(f"\n✅ 测试完成！")
        print(f"\n📋 建议:")
        print(f"  1. 查看分析结果，重点关注内心独白比例")
        print(f"  2. 如果效果良好，可以重新生成完整剧集")
        print(f"  3. 继续调整提示词以进一步优化")
    else:
        print(f"\n❌ 测试失败，请检查配置")

if __name__ == "__main__":
    main()
