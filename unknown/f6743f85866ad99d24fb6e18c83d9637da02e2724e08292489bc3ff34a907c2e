#!/usr/bin/env python3
"""
框架测试脚本
验证动画剧本生成框架的基本功能
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from generate_animation_drama import AnimationDramaGenerator
from config import logger

def test_framework_initialization():
    """测试框架初始化"""
    logger.info("=== 测试框架初始化 ===")
    
    try:
        generator = AnimationDramaGenerator(
            novel_path="2-animation-drama/raw_text/save_witch_whole.txt",
            project_name="save_witch_whole"
        )
        
        logger.info("✅ 框架初始化成功")
        logger.info(f"项目名称: {generator.project_name}")
        logger.info(f"输出目录: {generator.output_dir}")
        logger.info(f"小说路径: {generator.novel_path}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 框架初始化失败: {e}")
        return False

def test_status_functionality():
    """测试状态功能"""
    logger.info("=== 测试状态功能 ===")
    
    try:
        generator = AnimationDramaGenerator(
            novel_path="2-animation-drama/raw_text/save_witch_whole.txt",
            project_name="save_witch_whole"
        )
        
        status = generator.get_status()
        
        logger.info("✅ 状态获取成功")
        logger.info(f"项目名称: {status['project_name']}")
        logger.info(f"已完成阶段: {status['stages_completed']}")
        logger.info(f"找到剧集数量: {status['episodes_found']}")
        logger.info(f"小说文件存在: {status['files']['novel_exists']}")
        logger.info(f"摘要文件存在: {status['files']['summary_exists']}")
        
        # 显示剧集状态
        for episode in status['files']['episodes']:
            logger.info(f"剧集 {episode['name']}: 脚本{'✓' if episode['script_exists'] else '✗'} "
                       f"音频{'✓' if episode['audio_exists'] else '✗'} "
                       f"时间轴{'✓' if episode['timing_exists'] else '✗'} "
                       f"视频{'✓' if episode['video_exists'] else '✗'}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 状态功能测试失败: {e}")
        return False

def test_episode_file_detection():
    """测试剧集文件检测"""
    logger.info("=== 测试剧集文件检测 ===")
    
    try:
        generator = AnimationDramaGenerator(
            novel_path="2-animation-drama/raw_text/save_witch_whole.txt",
            project_name="save_witch_whole"
        )
        
        episode_files = generator.get_episode_files()
        
        logger.info(f"✅ 检测到 {len(episode_files)} 个剧集文件")
        for episode_file in episode_files:
            logger.info(f"  - {episode_file.name}")
            
            # 检查文件结构
            try:
                with open(episode_file, 'r', encoding='utf-8') as f:
                    episode_data = json.load(f)
                
                if "ep" in episode_data:
                    ep_data = episode_data["ep"]
                    logger.info(f"    剧集 {ep_data.get('ep_n', '?')}: {ep_data.get('t', '无标题')}")
                    logger.info(f"    角色数量: {len(ep_data.get('c', []))}")
                    logger.info(f"    场景数量: {len(ep_data.get('scenes', []))}")
                else:
                    logger.warning(f"    {episode_file.name} 使用旧格式")
                    
            except Exception as e:
                logger.error(f"    解析 {episode_file.name} 失败: {e}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 剧集文件检测失败: {e}")
        return False

def test_config_generation():
    """测试配置文件生成"""
    logger.info("=== 测试配置文件生成 ===")
    
    try:
        generator = AnimationDramaGenerator(
            novel_path="2-animation-drama/raw_text/save_witch_whole.txt",
            project_name="save_witch_whole"
        )
        
        episode_files = generator.get_episode_files()
        if not episode_files:
            logger.warning("没有找到剧集文件，跳过配置生成测试")
            return True
        
        # 测试第一个剧集的配置生成
        episode_file = episode_files[0]
        logger.info(f"测试配置生成: {episode_file.name}")
        
        config_file = generator.create_image_config(episode_file)
        
        if config_file.exists():
            logger.info("✅ 配置文件生成成功")
            
            # 检查配置文件内容
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            logger.info(f"背景设置: {config_data.get('background', {}).get('setting', '未设置')}")
            logger.info(f"角色数量: {len(config_data.get('characters', {}))}")
            
            for char_name, char_info in config_data.get('characters', {}).items():
                logger.info(f"  角色 {char_name}: {char_info.get('gender', '?')} {char_info.get('age', '?')}")
            
            # 清理测试文件
            config_file.unlink()
            logger.info("测试配置文件已清理")
            
        return True
    except Exception as e:
        logger.error(f"❌ 配置文件生成测试失败: {e}")
        return False

def test_stage_management():
    """测试阶段管理"""
    logger.info("=== 测试阶段管理 ===")
    
    try:
        generator = AnimationDramaGenerator(
            novel_path="2-animation-drama/raw_text/save_witch_whole.txt",
            project_name="save_witch_whole"
        )
        
        # 测试阶段状态检查
        stages = ["summary_generation", "episode_generation", "audio_generation", 
                 "image_generation", "video_generation"]
        
        for stage in stages:
            completed = generator.is_stage_completed(stage)
            logger.info(f"阶段 {stage}: {'✅ 已完成' if completed else '❌ 未完成'}")
        
        logger.info("✅ 阶段管理功能正常")
        return True
    except Exception as e:
        logger.error(f"❌ 阶段管理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始框架功能测试")
    
    tests = [
        ("框架初始化", test_framework_initialization),
        ("状态功能", test_status_functionality),
        ("剧集文件检测", test_episode_file_detection),
        ("配置文件生成", test_config_generation),
        ("阶段管理", test_stage_management)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"测试结果: {passed}/{total} 通过")
    logger.info(f"成功率: {passed/total*100:.1f}%")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！框架功能正常")
        return 0
    else:
        logger.warning(f"⚠️  有 {total-passed} 个测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
