#!/usr/bin/env python3
"""
测试脚本改进效果的验证工具
用于对比改进前后的剧本质量
"""

import os
import sys
import json
import argparse
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import logger, ANIMATION_DRAMA_DIR, ANIMATION_TEMP_DIR
from modules.langchain_interface import call_llm
from modules.gpt_parameters import (
    LLM_PROVIDER_AZURE,
    MODEL_KEY_GPT41,
    get_gpt_parameters
)

def analyze_script_quality(script_content: str) -> dict:
    """分析剧本质量的各个维度"""
    
    analysis_prompt = """
    请分析以下剧本的质量，从以下维度评分（1-10分）：
    
    1. 节奏感 (Pacing): 剧本的节奏是否紧凑，是否能立即抓住观众注意力
    2. 吸引力 (Engagement): 内容是否有趣，是否能持续吸引观众
    3. 对话质量 (Dialogue): 对话是否自然、有层次、有个性
    4. 冲突张力 (Tension): 是否有足够的冲突和戏剧张力
    5. 角色塑造 (Character): 角色是否有鲜明个性和深度
    6. 情节发展 (Plot): 情节是否有逻辑性和意外性
    
    剧本内容：
    {script_content}
    
    请返回JSON格式的分析结果：
    {{
        "scores": {{
            "pacing": 分数,
            "engagement": 分数,
            "dialogue": 分数,
            "tension": 分数,
            "character": 分数,
            "plot": 分数
        }},
        "overall_score": 总分,
        "strengths": ["优点1", "优点2", ...],
        "weaknesses": ["缺点1", "缺点2", ...],
        "specific_feedback": {{
            "pacing_notes": "节奏方面的具体反馈",
            "engagement_notes": "吸引力方面的具体反馈",
            "dialogue_notes": "对话方面的具体反馈",
            "tension_notes": "张力方面的具体反馈",
            "character_notes": "角色方面的具体反馈",
            "plot_notes": "情节方面的具体反馈"
        }}
    }}
    """
    
    try:
        response = call_llm(
            api_function="analyze_script_quality",
            prompt_data={"script_content": script_content},
            llm_type=LLM_PROVIDER_AZURE,
            model_key=MODEL_KEY_GPT41,
            using_cache=False,
            custom_prompt=analysis_prompt
        )
        
        if response:
            # 尝试解析JSON响应
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                # 如果不是JSON格式，返回文本分析
                return {"raw_analysis": response}
        
        return {"error": "No response from LLM"}
        
    except Exception as e:
        logger.error(f"分析剧本质量时出错: {e}")
        return {"error": str(e)}

def compare_scripts(original_script: str, improved_script: str) -> dict:
    """对比两个剧本的质量差异"""
    
    logger.info("分析原始剧本...")
    original_analysis = analyze_script_quality(original_script)
    
    logger.info("分析改进剧本...")
    improved_analysis = analyze_script_quality(improved_script)
    
    # 计算改进幅度
    comparison = {
        "original_analysis": original_analysis,
        "improved_analysis": improved_analysis,
        "improvements": {},
        "timestamp": datetime.now().isoformat()
    }
    
    if "scores" in original_analysis and "scores" in improved_analysis:
        orig_scores = original_analysis["scores"]
        impr_scores = improved_analysis["scores"]
        
        for metric in orig_scores:
            if metric in impr_scores:
                improvement = impr_scores[metric] - orig_scores[metric]
                comparison["improvements"][metric] = {
                    "original": orig_scores[metric],
                    "improved": impr_scores[metric],
                    "change": improvement,
                    "percentage": (improvement / orig_scores[metric] * 100) if orig_scores[metric] > 0 else 0
                }
    
    return comparison

def test_single_episode(episode_number: int = 1):
    """测试单个剧集的改进效果"""
    
    # 查找原始剧本文件
    temp_dir = Path(ANIMATION_DRAMA_DIR) / "temp"
    original_files = list(temp_dir.glob(f"full_script_{episode_number:02d}_text_*.txt"))
    refined_files = list(temp_dir.glob(f"full_script_{episode_number:02d}_refined_*.txt"))
    
    if not original_files:
        logger.error(f"未找到第{episode_number}集的原始剧本文件")
        return None
    
    if not refined_files:
        logger.error(f"未找到第{episode_number}集的精修剧本文件")
        return None
    
    # 使用最新的文件
    original_file = sorted(original_files)[-1]
    refined_file = sorted(refined_files)[-1]
    
    logger.info(f"对比文件:")
    logger.info(f"  原始: {original_file}")
    logger.info(f"  精修: {refined_file}")
    
    # 读取文件内容
    try:
        with open(original_file, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        with open(refined_file, 'r', encoding='utf-8') as f:
            refined_content = f.read()
        
        # 进行对比分析
        comparison = compare_scripts(original_content, refined_content)
        
        # 保存分析结果
        output_file = temp_dir / f"script_quality_comparison_{episode_number:02d}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(comparison, f, ensure_ascii=False, indent=2)
        
        logger.info(f"分析结果已保存到: {output_file}")
        
        # 打印简要结果
        print_comparison_summary(comparison)
        
        return comparison
        
    except Exception as e:
        logger.error(f"读取或分析文件时出错: {e}")
        return None

def print_comparison_summary(comparison: dict):
    """打印对比结果摘要"""
    
    print("\n" + "="*60)
    print("剧本质量对比分析结果")
    print("="*60)
    
    if "improvements" in comparison and comparison["improvements"]:
        print("\n📊 各维度评分对比:")
        print("-" * 40)
        
        for metric, data in comparison["improvements"].items():
            metric_names = {
                "pacing": "节奏感",
                "engagement": "吸引力", 
                "dialogue": "对话质量",
                "tension": "冲突张力",
                "character": "角色塑造",
                "plot": "情节发展"
            }
            
            name = metric_names.get(metric, metric)
            original = data["original"]
            improved = data["improved"]
            change = data["change"]
            percentage = data["percentage"]
            
            status = "📈" if change > 0 else "📉" if change < 0 else "➡️"
            print(f"{status} {name}: {original:.1f} → {improved:.1f} ({change:+.1f}, {percentage:+.1f}%)")
    
    # 显示具体反馈
    if "improved_analysis" in comparison and "specific_feedback" in comparison["improved_analysis"]:
        print("\n💡 改进后的具体反馈:")
        print("-" * 40)
        feedback = comparison["improved_analysis"]["specific_feedback"]
        
        for key, value in feedback.items():
            if value and isinstance(value, str):
                area = key.replace("_notes", "").replace("_", " ").title()
                print(f"• {area}: {value}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试剧本改进效果")
    parser.add_argument("--episode", type=int, default=1, help="要测试的剧集编号")
    parser.add_argument("--all", action="store_true", help="测试所有可用的剧集")
    
    args = parser.parse_args()
    
    if args.all:
        # 测试所有剧集
        temp_dir = Path(ANIMATION_DRAMA_DIR) / "temp"
        episode_files = temp_dir.glob("full_script_*_text_*.txt")
        episode_numbers = set()
        
        for file in episode_files:
            # 从文件名提取剧集编号
            parts = file.stem.split("_")
            if len(parts) >= 3 and parts[2].isdigit():
                episode_numbers.add(int(parts[2]))
        
        for episode_num in sorted(episode_numbers):
            logger.info(f"\n测试第{episode_num}集...")
            test_single_episode(episode_num)
    else:
        # 测试单个剧集
        test_single_episode(args.episode)

if __name__ == "__main__":
    main()
