#!/usr/bin/env python3
"""
测试动画剧本生成系统的增强功能

这个脚本测试新增的三个核心功能：
1. 角色心理分析 (analyze_character_psychology)
2. 对话心理增强 (enhance_dialogue_psychology)
3. 场景描述增强 (enhance_scene_descriptions)
"""

import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.langchain_interface import call_llm_json_response
from generate_episodes import (
    CHARACTER_PSYCHOLOGY_FRAMEWORK,
    SCENE_DESCRIPTION_FRAMEWORK,
    EPISODE_QUALITY_STANDARDS
)

def test_character_psychology_analysis():
    """测试角色心理分析功能"""
    print("\n=== 测试角色心理分析功能 ===")
    
    # 测试数据
    test_character = {
        "character_name": "罗兰·温布顿（程岩）",
        "character_background": {
            "name": "罗兰·温布顿（程岩）",
            "gender": "male",
            "age": "Adult",
            "role": ["Protagonist", "Narrator", "Prince", "Transmigrator"],
            "aliases": ["罗兰", "程岩", "殿下", "灰堡四王子"]
        },
        "story_context": {
            "setting": "中世纪奇幻世界",
            "main_conflict": "面对陌生世界的自我定位与疑虑",
            "world_tone": "modern"
        },
        "current_situation": "第1集的情节发展 - 刚刚穿越到异世界，面临身份危机和生存压力",
        "psychology_framework": CHARACTER_PSYCHOLOGY_FRAMEWORK
    }
    
    try:
        # 调用心理分析函数
        psychology_result = call_llm_json_response(
            "analyze_character_psychology",
            test_character,
            using_cache=False,
            max_retries=2
        )
        
        if psychology_result:
            print("✅ 角色心理分析成功")
            print(f"分析结果摘要:")
            if "character_psychology" in psychology_result:
                psych = psychology_result["character_psychology"]
                print(f"- 核心动机: {psych.get('core_traits', {}).get('internal_motivations', [])[:2]}")
                print(f"- 当前情绪: {psych.get('current_state', {}).get('immediate_emotions', [])[:2]}")
                print(f"- 语言特征: {psych.get('behavioral_profile', {}).get('speech_patterns', 'N/A')[:100]}...")
        else:
            print("❌ 角色心理分析失败")
            return False
            
    except Exception as e:
        print(f"❌ 角色心理分析异常: {str(e)}")
        return False
    
    return True

def test_dialogue_enhancement():
    """测试对话心理增强功能"""
    print("\n=== 测试对话心理增强功能 ===")
    
    # 测试数据 - 来自真实剧本的对话
    test_dialogue_data = {
        "original_dialogues": [
            {
                "c": "巴罗夫",
                "m": "Nervous", 
                "t": "殿下，午时三刻了。您知道的……暴民需要鲜血来平息。"
            },
            {
                "c": "罗兰",
                "m": "Angry",
                "t": "哈！好得很！本王子还没睡醒，你们倒急着看戏？"
            }
        ],
        "character_psychologies": {
            "巴罗夫": {
                "core_traits": {
                    "internal_motivations": ["维护自身地位", "避免政治风险"],
                    "emotional_patterns": ["谨慎观察", "明哲保身"]
                },
                "current_state": {
                    "immediate_emotions": ["紧张", "担忧"],
                    "suppressed_feelings": ["对主人的怀疑", "自我保护欲"]
                }
            },
            "罗兰": {
                "core_traits": {
                    "internal_motivations": ["适应新身份", "建立权威"],
                    "emotional_patterns": ["内心混乱但表面坚强", "用愤怒掩盖恐惧"]
                },
                "current_state": {
                    "immediate_emotions": ["困惑", "愤怒", "压力"],
                    "internal_conflicts": ["现代人价值观vs王子身份要求"]
                }
            }
        },
        "scene_context": "众人聚集在绞刑架前，等待王子执行处决令",
        "enhancement_guidelines": CHARACTER_PSYCHOLOGY_FRAMEWORK
    }
    
    try:
        enhanced_result = call_llm_json_response(
            "enhance_dialogue_psychology", 
            test_dialogue_data,
            using_cache=False,
            max_retries=2
        )
        
        if enhanced_result and "enhanced_dialogues" in enhanced_result:
            print("✅ 对话心理增强成功")
            print("增强前后对比:")
            
            original_dialogues = test_dialogue_data["original_dialogues"]
            enhanced_dialogues = enhanced_result["enhanced_dialogues"]
            
            for i, (orig, enhanced) in enumerate(zip(original_dialogues, enhanced_dialogues)):
                print(f"\n角色 {i+1}: {orig['c']}")
                print(f"原版: {orig['t']}")
                print(f"增强: {enhanced.get('t', 'N/A')}")
                if "psychological_elements" in enhanced:
                    print(f"潜台词: {enhanced['psychological_elements'].get('subtext', 'N/A')}")
        else:
            print("❌ 对话心理增强失败")
            return False
            
    except Exception as e:
        print(f"❌ 对话心理增强异常: {str(e)}")
        return False
    
    return True

def test_scene_enhancement():
    """测试场景描述增强功能"""
    print("\n=== 测试场景描述增强功能 ===")
    
    # 测试数据 - 来自真实剧本的场景
    test_scene_data = {
        "original_scenes": [
            {
                "scene_number": 1,
                "n": 1,
                "sn": 1,
                "shot_type": "establishing",
                "environment": {
                    "image": "Bleak medieval town square at dawn; gallows with swinging iron chains, crowd murmurs, cold wind, oppressive and tense."
                },
                "narration": {
                    "nr": "Blood and rot fill the air; the protagonist's senses are assaulted by deathly smells and the roar of a hostile crowd—he realizes he's no longer in his office."
                },
                "dialogue": [],
                "sound_cues": ["Heartbeat growing louder", "metallic hum", "sudden wind"]
            }
        ],
        "emotional_arc": {
            "opening_emotion": "紧张与困惑",
            "midpoint_emotion": "冲突与抉择",
            "climax_emotion": "决心与行动", 
            "resolution_emotion": "希望与转变"
        },
        "character_emotional_states": {
            "罗兰": {
                "current_emotions": ["震惊", "困惑", "恐惧"],
                "psychological_state": "刚穿越的极度不适应状态"
            }
        },
        "description_framework": SCENE_DESCRIPTION_FRAMEWORK,
        "quality_standards": EPISODE_QUALITY_STANDARDS
    }
    
    try:
        enhanced_result = call_llm_json_response(
            "enhance_scene_descriptions",
            test_scene_data,
            using_cache=False, 
            max_retries=2
        )
        
        if enhanced_result and "enhanced_scenes" in enhanced_result:
            print("✅ 场景描述增强成功")
            print("增强前后对比:")
            
            original_scene = test_scene_data["original_scenes"][0]
            enhanced_scene = enhanced_result["enhanced_scenes"][0]
            
            print(f"\n=== 环境描述 ===")
            print(f"原版: {original_scene['environment']['image']}")
            print(f"增强: {enhanced_scene.get('environment', {}).get('image', 'N/A')}")
            
            print(f"\n=== 叙述描述 ===")
            print(f"原版: {original_scene['narration']['nr']}")
            print(f"增强: {enhanced_scene.get('narration', {}).get('nr', 'N/A')}")
            
            print(f"\n=== 音效提示 ===")
            print(f"原版: {original_scene.get('sound_cues', [])}")
            print(f"增强: {enhanced_scene.get('sound_cues', [])}")
            
        else:
            print("❌ 场景描述增强失败")
            return False
            
    except Exception as e:
        print(f"❌ 场景描述增强异常: {str(e)}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始测试动画剧本生成系统的增强功能...")
    
    # 存储测试结果
    results = []
    
    # 测试各个功能
    results.append(test_character_psychology_analysis())
    results.append(test_dialogue_enhancement())  
    results.append(test_scene_enhancement())
    
    # 总结测试结果
    print("\n" + "="*50)
    print("测试结果总结:")
    print(f"角色心理分析: {'✅ 通过' if results[0] else '❌ 失败'}")
    print(f"对话心理增强: {'✅ 通过' if results[1] else '❌ 失败'}")
    print(f"场景描述增强: {'✅ 通过' if results[2] else '❌ 失败'}")
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n整体成功率: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("🎉 所有增强功能测试通过！系统已准备好生成更优质的剧本。")
    elif success_rate >= 66:
        print("⚠️  部分功能需要调试，但核心功能可用。")
    else:
        print("❌ 多个功能存在问题，需要进一步调试。")

if __name__ == "__main__":
    main() 